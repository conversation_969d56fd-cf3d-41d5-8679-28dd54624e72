export enum ReconciliationStatus {
    All = 'All',
    Unreconciled = 'Unreconciled',
}

export enum TransactionType {
    CRIMSON = 'CRIMSON',
    BANK = 'BANK',
}

export enum MoneyType {
    Contribution = 'Contribution',
    OtherReceipt = 'Other Receipt',
    Disbursement = 'Disbursement',
    Chargeback = 'Chargeback',
    Debit = 'Debit',
    Winred = 'Winred',
    WinredChargeback = 'Winred Chargeback',
}

export interface CrimsonTransaction {
    id: string;
    date: string;
    moneyType: MoneyType;
    paymentType: string;
    amount: number;
    group: string;
    isReconciled: boolean;
    batchDetails?: { id: string, amount: number, donor: string }[];
}

export interface BankTransaction {
    id: string;
    date: string;
    description: string;
    amount: number;
    isReconciled: boolean;
    isNrit?: boolean;
    splitDetails?: { gross: number, chargebacks: number, fees: number };
}

export type AnyTransaction = CrimsonTransaction | BankTransaction;

export interface MatchedPair {
  crimsonTransactionId: string;
  bankTransactionId: string[];
  confidenceScore: number;
  reasoning: string;
}