import React, { useState, useMemo, useCallback } from 'react';
import { ReconciliationPanel } from './components/ReconciliationPanel';
import { CrimsonTransaction, BankTransaction, ReconciliationStatus, TransactionType, MatchedPair, AnyTransaction } from './types';
import { FilterOptions } from './components/FilterBar';
import { initialCrimsonTransactions, initialBankTransactions } from './constants';
import { reconcileWithAI } from './services/geminiService';
import { Header } from './components/Header';
import { ReconciliationActions } from './components/ReconciliationActions';
import { Dashboard } from './components/Dashboard';
import { BulkActions } from './components/BulkActions';
import { WorkflowGuide } from './components/WorkflowGuide';
import { RulesEngine, ReconciliationRule } from './components/RulesEngine';
import { AuditTrail, AuditEntry } from './components/AuditTrail';
import { ExportModal } from './components/ExportModal';
import { DataValidation } from './components/DataValidation';
import { LoadingIcon, SparklesIcon, CheckCircleIcon, XCircleIcon, LightBulbIcon, AcademicCapIcon, CogIcon, ClockIcon, DocumentArrowDownIcon, ExclamationTriangleIcon } from './components/Icons';
import { ImportModal } from './components/ImportModal';
import { SplitTransactionModal } from './components/SplitTransactionModal';

const App: React.FC = () => {
    const [crimsonTransactions, setCrimsonTransactions] = useState<CrimsonTransaction[]>(initialCrimsonTransactions);
    const [bankTransactions, setBankTransactions] = useState<BankTransaction[]>(initialBankTransactions);

    const [crimsonStatusFilter, setCrimsonStatusFilter] = useState<ReconciliationStatus>(ReconciliationStatus.Unreconciled);
    const [bankStatusFilter, setBankStatusFilter] = useState<ReconciliationStatus>(ReconciliationStatus.Unreconciled);

    const [selectedCrimsonIds, setSelectedCrimsonIds] = useState<Set<string>>(new Set());
    const [selectedBankIds, setSelectedBankIds] = useState<Set<string>>(new Set());
    
    const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
    const [aiSuggestions, setAiSuggestions] = useState<MatchedPair[]>([]);

    const [isLoading, setIsLoading] = useState(false);
    const [aiMessage, setAiMessage] = useState<string | null>(null);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [transactionToSplit, setTransactionToSplit] = useState<BankTransaction | null>(null);
    const [showWorkflowGuide, setShowWorkflowGuide] = useState(false);
    const [showRulesEngine, setShowRulesEngine] = useState(false);
    const [showAuditTrail, setShowAuditTrail] = useState(false);
    const [showExportModal, setShowExportModal] = useState(false);
    const [showDataValidation, setShowDataValidation] = useState(false);

    // Rules and audit state
    const [reconciliationRules, setReconciliationRules] = useState<ReconciliationRule[]>([]);
    const [auditEntries, setAuditEntries] = useState<AuditEntry[]>([
        {
            id: 'audit-1',
            timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            user: 'Sarah Johnson',
            action: 'ai_suggest',
            details: 'AI suggested 3 potential matches with high confidence',
            transactionIds: ['C1', 'B1', 'B2'],
            confidence: 0.92
        },
        {
            id: 'audit-2',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            user: 'Sarah Johnson',
            action: 'reconcile',
            details: 'Manually reconciled WinRed payout transaction',
            transactionIds: ['C7', 'C8', 'C9', 'B11'],
            amount: 833.55
        },
        {
            id: 'audit-3',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            user: 'Mike Chen',
            action: 'import',
            details: 'Imported 15 bank transactions from CSV file',
            transactionIds: ['B12', 'B13', 'B14', 'B15', 'B16'],
            metadata: { fileName: 'bank_statement_april.csv', recordCount: 15 }
        }
    ]);

    // Filter states
    const [crimsonFilters, setCrimsonFilters] = useState<FilterOptions>({
        statusFilter: ReconciliationStatus.Unreconciled,
        searchText: '',
        dateRange: { start: '', end: '' },
        amountRange: { min: null, max: null },
        fundCode: 'All',
        accountCode: 'All',
        paymentType: 'All',
        showAdvanced: false,
    });

    const [bankFilters, setBankFilters] = useState<FilterOptions>({
        statusFilter: ReconciliationStatus.Unreconciled,
        searchText: '',
        dateRange: { start: '', end: '' },
        amountRange: { min: null, max: null },
        fundCode: 'All',
        accountCode: 'All',
        paymentType: 'All',
        showAdvanced: false,
    });

    // Enhanced filtering logic
    const applyFilters = useCallback((transactions: AnyTransaction[], filters: FilterOptions, isCrimson: boolean) => {
        return transactions.filter(transaction => {
            // Status filter
            if (filters.statusFilter === ReconciliationStatus.Unreconciled && transaction.isReconciled) {
                // Show reconciled transactions only if they have AI suggestions
                const hasAISuggestion = isCrimson
                    ? aiSuggestions.some(s => s.crimsonTransactionId === transaction.id)
                    : aiSuggestions.some(s => s.bankTransactionId.includes(transaction.id));
                if (!hasAISuggestion) return false;
            }

            // Search text filter
            if (filters.searchText) {
                const searchLower = filters.searchText.toLowerCase();
                const description = isCrimson
                    ? (transaction as CrimsonTransaction).paymentType
                    : (transaction as BankTransaction).description;
                const amount = transaction.amount.toString();
                const date = transaction.date;

                if (!description.toLowerCase().includes(searchLower) &&
                    !amount.includes(searchLower) &&
                    !date.includes(searchLower)) {
                    return false;
                }
            }

            // Date range filter
            if (filters.dateRange.start || filters.dateRange.end) {
                const transactionDate = new Date(transaction.date);
                if (filters.dateRange.start && transactionDate < new Date(filters.dateRange.start)) return false;
                if (filters.dateRange.end && transactionDate > new Date(filters.dateRange.end)) return false;
            }

            // Amount range filter
            if (filters.amountRange.min !== null && transaction.amount < filters.amountRange.min) return false;
            if (filters.amountRange.max !== null && transaction.amount > filters.amountRange.max) return false;

            // Fund code / transaction type filter
            if (filters.fundCode !== 'All') {
                if (isCrimson) {
                    // For Crimson, filter by fund code (would need to be added to transaction data)
                    // For now, we'll skip this filter for Crimson
                } else {
                    // For Bank, filter by credit/debit
                    if (filters.fundCode === 'Credit' && transaction.amount < 0) return false;
                    if (filters.fundCode === 'Debit' && transaction.amount >= 0) return false;
                }
            }

            // Payment type / description filter
            if (filters.paymentType !== 'All') {
                if (isCrimson) {
                    const paymentType = (transaction as CrimsonTransaction).paymentType;
                    if (!paymentType.includes(filters.paymentType)) return false;
                } else {
                    const description = (transaction as BankTransaction).description.toLowerCase();
                    if (!description.includes(filters.paymentType.toLowerCase())) return false;
                }
            }

            return true;
        });
    }, [aiSuggestions]);

    const filteredCrimsonTransactions = useMemo(() => {
        return applyFilters(crimsonTransactions, crimsonFilters, true);
    }, [crimsonTransactions, crimsonFilters, applyFilters]);

    const filteredBankTransactions = useMemo(() => {
        return applyFilters(bankTransactions, bankFilters, false);
    }, [bankTransactions, bankFilters, applyFilters]);

    const handleSuggestReconciliation = useCallback(async () => {
        setIsLoading(true);
        setAiMessage("AI is analyzing transactions...");
        setAiSuggestions([]);

        const unreconciledCrimson = crimsonTransactions.filter(t => !t.isReconciled);
        const unreconciledBank = bankTransactions.filter(t => !t.isReconciled);

        try {
            const matches = await reconcileWithAI(unreconciledCrimson, unreconciledBank);
            if (matches && matches.length > 0) {
                setAiSuggestions(matches);
                setAiMessage(`AI has found ${matches.length} potential match(es) for your review.`);
            } else {
                setAiMessage("AI could not find any clear matches.");
                setTimeout(() => setAiMessage(null), 5000);
            }
        } catch (error) {
            console.error("AI Reconciliation Error:", error);
            setAiMessage("An error occurred during AI analysis.");
            setTimeout(() => setAiMessage(null), 5000);
        } finally {
            setIsLoading(false);
        }
    }, [crimsonTransactions, bankTransactions]);

    // Audit logging function
    const addAuditEntry = useCallback((entry: Omit<AuditEntry, 'id' | 'timestamp' | 'user'>) => {
        const newEntry: AuditEntry = {
            ...entry,
            id: `audit-${Date.now()}`,
            timestamp: new Date(),
            user: 'Sarah Johnson' // In real app, get from auth context
        };
        setAuditEntries(prev => [newEntry, ...prev]);
    }, []);

    const updateReconciliationStatus = (idsToReconcile: { crimson: string[], bank: string[] }) => {
        const reconcile = (transactions: AnyTransaction[], ids: Set<string>) =>
            transactions.map(t => ids.has(t.id) ? { ...t, isReconciled: true } : t);

        setCrimsonTransactions(prev => reconcile(prev, new Set(idsToReconcile.crimson)) as CrimsonTransaction[]);
        setBankTransactions(prev => reconcile(prev, new Set(idsToReconcile.bank)) as BankTransaction[]);

        // Log reconciliation action
        const totalAmount = [...idsToReconcile.crimson, ...idsToReconcile.bank]
            .map(id => {
                const crimsonTx = crimsonTransactions.find(t => t.id === id);
                const bankTx = bankTransactions.find(t => t.id === id);
                return crimsonTx?.amount || bankTx?.amount || 0;
            })
            .reduce((sum, amount) => sum + Math.abs(amount), 0);

        addAuditEntry({
            action: 'reconcile',
            details: `Reconciled ${idsToReconcile.crimson.length + idsToReconcile.bank.length} transactions`,
            transactionIds: [...idsToReconcile.crimson, ...idsToReconcile.bank],
            amount: totalAmount
        });
    };

    const handleAcceptSuggestions = () => {
        const idsToReconcile = aiSuggestions.reduce((acc, suggestion) => {
            acc.crimson.push(suggestion.crimsonTransactionId);
            acc.bank.push(...suggestion.bankTransactionId);
            return acc;
        }, { crimson: [] as string[], bank: [] as string[] });
        
        updateReconciliationStatus(idsToReconcile);
        setAiMessage(`Accepted ${aiSuggestions.length} AI suggestion(s).`);
        setAiSuggestions([]);
        setTimeout(() => setAiMessage(null), 5000);
    };

    const handleDeclineSuggestions = () => {
        setAiSuggestions([]);
        setAiMessage("AI suggestions cleared.");
        setTimeout(() => setAiMessage(null), 5000);
    };

    const handleManualReconcile = () => {
        updateReconciliationStatus({ crimson: Array.from(selectedCrimsonIds), bank: Array.from(selectedBankIds) });
        setSelectedCrimsonIds(new Set());
        setSelectedBankIds(new Set());
    };
    
    const handleImportTransactions = (newTransactions: BankTransaction[]) => {
        setBankTransactions(prev => [...newTransactions, ...prev]);
        setIsImportModalOpen(false);
        // Automatically trigger AI analysis after import
        setTimeout(handleSuggestReconciliation, 100);
    };
    
     const handleConfirmSplit = (originalId: string, splits: Omit<BankTransaction, 'id' | 'isReconciled'>[]) => {
        setBankTransactions(prev => {
            const newTransactions = [...prev];
            const index = newTransactions.findIndex(t => t.id === originalId);
            if (index === -1) return prev; // Should not happen

            const newSplitTransactions: BankTransaction[] = splits.map((split, i) => ({
                ...split,
                id: `${originalId}-split-${i}`,
                isReconciled: false,
            }));

            newTransactions.splice(index, 1, ...newSplitTransactions);
            return newTransactions;
        });
        setTransactionToSplit(null);
    };

    const handleMarkAsNrit = (transactionId: string) => {
        setBankTransactions(prev => 
            prev.map(t => 
                t.id === transactionId 
                    ? { ...t, isReconciled: true, isNrit: true } 
                    : t
            )
        );
    };

    const handleUnmarkAsNrit = (transactionId: string) => {
        setBankTransactions(prev =>
            prev.map(t =>
                t.id === transactionId
                    ? { ...t, isReconciled: false, isNrit: false }
                    : t
            )
        );
    };

    // Bulk operation handlers
    const handleBulkReconcile = useCallback((selectedIds: { crimson: string[], bank: string[] }) => {
        updateReconciliationStatus(selectedIds);
        setSelectedCrimsonIds(new Set());
        setSelectedBankIds(new Set());
    }, []);

    const handleBulkMarkAsNrit = useCallback((selectedBankIds: string[]) => {
        setBankTransactions(prev =>
            prev.map(t =>
                selectedBankIds.includes(t.id)
                    ? { ...t, isReconciled: true, isNrit: true }
                    : t
            )
        );
        setSelectedBankIds(new Set());
    }, []);

    const handleBulkExport = useCallback((selectedIds: string[], isCrimson: boolean) => {
        const transactions = isCrimson ? crimsonTransactions : bankTransactions;
        const selectedTransactions = transactions.filter(t => selectedIds.includes(t.id));

        // Create CSV content
        const headers = isCrimson
            ? ['ID', 'Date', 'Money Type', 'Payment Type', 'Amount', 'Group', 'Reconciled']
            : ['ID', 'Date', 'Description', 'Amount', 'Reconciled', 'NRIT'];

        const csvContent = [
            headers.join(','),
            ...selectedTransactions.map(t => {
                if (isCrimson) {
                    const ct = t as CrimsonTransaction;
                    return [ct.id, ct.date, ct.moneyType, ct.paymentType, ct.amount, ct.group, ct.isReconciled].join(',');
                } else {
                    const bt = t as BankTransaction;
                    return [bt.id, bt.date, `"${bt.description}"`, bt.amount, bt.isReconciled, bt.isNrit || false].join(',');
                }
            })
        ].join('\n');

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${isCrimson ? 'crimson' : 'bank'}_transactions_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }, [crimsonTransactions, bankTransactions]);


    const selectedCrimsonTotal = useMemo(() => crimsonTransactions.filter(t => selectedCrimsonIds.has(t.id)).reduce((sum, t) => sum + t.amount, 0), [crimsonTransactions, selectedCrimsonIds]);
    const selectedBankTotal = useMemo(() => bankTransactions.filter(t => selectedBankIds.has(t.id)).reduce((sum, t) => sum + t.amount, 0), [bankTransactions, selectedBankIds]);
    
    const unreconciledCrimsonTotal = useMemo(() => crimsonTransactions.filter(t => !t.isReconciled).reduce((sum, t) => sum + t.amount, 0), [crimsonTransactions]);
    const unreconciledBankTotal = useMemo(() => bankTransactions.filter(t => !t.isReconciled).reduce((sum, t) => sum + t.amount, 0), [bankTransactions]);

    return (
        <div className="min-h-screen bg-slate-50 text-slate-800 flex flex-col">
            <Header />
            <main className="p-4 sm:p-6 lg:p-8 flex-grow flex flex-col">
                <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
                    <h1 className="text-3xl font-bold text-slate-800">Reconciliation Tool</h1>
                    <div className="flex items-center gap-2">
                        <button
                            onClick={() => setShowDataValidation(true)}
                            className="flex items-center gap-2 px-3 py-2 bg-orange-600 text-white font-medium rounded-lg shadow-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200"
                        >
                            <ExclamationTriangleIcon className="w-4 h-4" />
                            Validate
                        </button>
                        <button
                            onClick={() => setShowExportModal(true)}
                            className="flex items-center gap-2 px-3 py-2 bg-emerald-600 text-white font-medium rounded-lg shadow-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200"
                        >
                            <DocumentArrowDownIcon className="w-4 h-4" />
                            Export
                        </button>
                        <button
                            onClick={() => setShowAuditTrail(true)}
                            className="flex items-center gap-2 px-3 py-2 bg-slate-600 text-white font-medium rounded-lg shadow-md hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-all duration-200"
                        >
                            <ClockIcon className="w-4 h-4" />
                            Audit
                        </button>
                        <button
                            onClick={() => setShowRulesEngine(true)}
                            className="flex items-center gap-2 px-3 py-2 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200"
                        >
                            <CogIcon className="w-4 h-4" />
                            Rules
                        </button>
                        <button
                            onClick={() => setShowWorkflowGuide(true)}
                            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white font-semibold rounded-lg shadow-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200"
                        >
                            <AcademicCapIcon className="w-5 h-5" />
                            Workflow Guide
                        </button>
                         <button
                            onClick={handleSuggestReconciliation}
                            disabled={isLoading}
                            className="flex items-center gap-2 px-4 py-2 bg-sky-600 text-white font-semibold rounded-lg shadow-md hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 disabled:bg-sky-300 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            {isLoading ? <LoadingIcon /> : <SparklesIcon />}
                            {isLoading ? 'Analyzing...' : 'Suggest Matches'}
                        </button>
                    </div>
                </div>

                <Dashboard
                    crimsonTransactions={crimsonTransactions}
                    bankTransactions={bankTransactions}
                    aiSuggestions={aiSuggestions}
                />

                {aiMessage && (
                     <div className="flex items-center justify-center gap-4 mb-4 p-3 rounded-lg bg-sky-100 text-sky-800 border border-sky-200 shadow-sm">
                        <LightBulbIcon className="w-6 h-6 text-sky-600" />
                        <span className="text-sm font-medium">{aiMessage}</span>
                         {aiSuggestions.length > 0 && (
                            <div className="flex items-center gap-2 ml-auto">
                                <button onClick={handleAcceptSuggestions} className="flex items-center gap-1.5 px-3 py-1 bg-emerald-600 text-white rounded-md text-sm font-semibold hover:bg-emerald-700 transition-colors">
                                    <CheckCircleIcon className="w-4 h-4" /> Accept
                                </button>
                                <button onClick={handleDeclineSuggestions} className="flex items-center gap-1.5 px-3 py-1 bg-rose-600 text-white rounded-md text-sm font-semibold hover:bg-rose-700 transition-colors">
                                    <XCircleIcon className="w-4 h-4" /> Decline
                                </button>
                            </div>
                         )}
                     </div>
                )}


                {/* Bulk Actions */}
                <BulkActions
                    selectedCount={selectedCrimsonIds.size}
                    isCrimson={true}
                    onBulkReconcile={() => handleBulkReconcile({ crimson: Array.from(selectedCrimsonIds), bank: [] })}
                    onBulkExport={() => handleBulkExport(Array.from(selectedCrimsonIds), true)}
                    onClearSelection={() => setSelectedCrimsonIds(new Set())}
                />

                <BulkActions
                    selectedCount={selectedBankIds.size}
                    isCrimson={false}
                    onBulkReconcile={() => handleBulkReconcile({ crimson: [], bank: Array.from(selectedBankIds) })}
                    onBulkMarkAsNrit={() => handleBulkMarkAsNrit(Array.from(selectedBankIds))}
                    onBulkExport={() => handleBulkExport(Array.from(selectedBankIds), false)}
                    onClearSelection={() => setSelectedBankIds(new Set())}
                />

                <ReconciliationActions
                    crimsonSelectedTotal={selectedCrimsonTotal}
                    bankSelectedTotal={selectedBankTotal}
                    crimsonUnreconciledTotal={unreconciledCrimsonTotal}
                    bankUnreconciledTotal={unreconciledBankTotal}
                    onReconcile={handleManualReconcile}
                />

                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 mt-4 flex-grow min-h-0">
                    <ReconciliationPanel
                        title="Crimson Activity"
                        transactions={filteredCrimsonTransactions}
                        transactionType={TransactionType.CRIMSON}
                        statusFilter={crimsonStatusFilter}
                        onStatusFilterChange={(status) => {
                            setCrimsonStatusFilter(status);
                            setCrimsonFilters(prev => ({ ...prev, statusFilter: status }));
                        }}
                        selectedIds={selectedCrimsonIds}
                        onSelectionChange={setSelectedCrimsonIds}
                        aiSuggestions={aiSuggestions}
                        expandedRows={expandedRows}
                        onToggleExpand={setExpandedRows}
                        filters={crimsonFilters}
                        onFiltersChange={setCrimsonFilters}
                    />
                    <ReconciliationPanel
                        title="Bank Activity"
                        transactions={filteredBankTransactions}
                        transactionType={TransactionType.BANK}
                        statusFilter={bankStatusFilter}
                        onStatusFilterChange={(status) => {
                            setBankStatusFilter(status);
                            setBankFilters(prev => ({ ...prev, statusFilter: status }));
                        }}
                        selectedIds={selectedBankIds}
                        onSelectionChange={setSelectedBankIds}
                        aiSuggestions={aiSuggestions}
                        expandedRows={expandedRows}
                        onToggleExpand={setExpandedRows}
                        onOpenImportModal={() => setIsImportModalOpen(true)}
                        onOpenSplitModal={setTransactionToSplit}
                        onMarkAsNrit={handleMarkAsNrit}
                        onUnmarkAsNrit={handleUnmarkAsNrit}
                        filters={bankFilters}
                        onFiltersChange={setBankFilters}
                    />
                </div>
            </main>
            <ImportModal 
                isOpen={isImportModalOpen}
                onClose={() => setIsImportModalOpen(false)}
                onImport={handleImportTransactions}
            />
             <SplitTransactionModal
                isOpen={!!transactionToSplit}
                onClose={() => setTransactionToSplit(null)}
                transaction={transactionToSplit}
                onConfirmSplit={handleConfirmSplit}
            />

            <WorkflowGuide
                isVisible={showWorkflowGuide}
                onClose={() => setShowWorkflowGuide(false)}
                onSuggestMatches={handleSuggestReconciliation}
                unreconciledCount={unreconciledCrimsonTotal !== 0 ? crimsonTransactions.filter(t => !t.isReconciled).length : 0}
                aiSuggestionsCount={aiSuggestions.length}
                hasDiscrepancy={Math.abs(unreconciledCrimsonTotal - unreconciledBankTotal) > 0.01}
            />

            <RulesEngine
                isOpen={showRulesEngine}
                onClose={() => setShowRulesEngine(false)}
                rules={reconciliationRules}
                onSaveRules={(rules) => {
                    setReconciliationRules(rules);
                    addAuditEntry({
                        action: 'bulk_action',
                        details: `Updated reconciliation rules: ${rules.filter(r => r.isActive).length} active rules`,
                        transactionIds: []
                    });
                }}
            />

            <AuditTrail
                isOpen={showAuditTrail}
                onClose={() => setShowAuditTrail(false)}
                entries={auditEntries}
            />

            <ExportModal
                isOpen={showExportModal}
                onClose={() => setShowExportModal(false)}
                crimsonTransactions={crimsonTransactions}
                bankTransactions={bankTransactions}
            />

            <DataValidation
                isOpen={showDataValidation}
                onClose={() => setShowDataValidation(false)}
                crimsonTransactions={crimsonTransactions}
                bankTransactions={bankTransactions}
                onFixIssue={(issueId, transactionId) => {
                    // In a real app, this would navigate to the transaction or open an edit modal
                    console.log('Fix issue:', issueId, 'for transaction:', transactionId);
                    addAuditEntry({
                        action: 'bulk_action',
                        details: `Attempted to fix validation issue: ${issueId}`,
                        transactionIds: [transactionId]
                    });
                }}
            />
        </div>
    );
};

export default App;