import { GoogleGenAI, Type } from "@google/genai";
import { CrimsonTransaction, BankTransaction, MatchedPair } from "../types";

// Ensure the API key is available in the environment variables
if (!process.env.API_KEY) {
    // In a real app, you'd want to handle this more gracefully.
    // For this context, we assume it's set.
    console.warn("API_KEY environment variable not set. AI features will not work.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY! });

const responseSchema = {
    type: Type.ARRAY,
    items: {
        type: Type.OBJECT,
        properties: {
            crimsonTransactionId: {
                type: Type.STRING,
                description: 'The unique ID of the matched Crimson transaction.',
            },
            bankTransactionId: {
                type: Type.ARRAY,
                items: { type: Type.STRING },
                description: 'An array of unique IDs of the matched Bank transaction(s). ALWAYS use an array, even for a single match.',
            },
            confidenceScore: {
                type: Type.NUMBER,
                description: 'A score from 0 to 1 indicating the confidence of the match.',
            },
            reasoning: {
                type: Type.STRING,
                description: 'A brief explanation for why the transactions were matched.',
            },
        },
        required: ["crimsonTransactionId", "bankTransactionId", "confidenceScore", "reasoning"],
    },
};

export const reconcileWithAI = async (
    crimsonTransactions: CrimsonTransaction[],
    bankTransactions: BankTransaction[]
): Promise<MatchedPair[]> => {
    
    if (!process.env.API_KEY) {
        throw new Error("Gemini API key is not configured.");
    }
    
    if (crimsonTransactions.length === 0 || bankTransactions.length === 0) {
        return [];
    }
    
    const prompt = `
        You are an expert accounting assistant for political campaigns. Your task is to find matching financial records to help with reconciliation.
        Analyze the two JSON arrays of transactions: 'crimsonTransactions' (internal ledger) and 'bankTransactions' (bank statement).

        Match transactions from 'crimsonTransactions' to one or more transactions in 'bankTransactions'.

        Matching criteria:
        1.  **Amount:** A positive amount in Crimson should match a positive amount in the Bank. A negative amount should match a negative amount.
        2.  **Date Proximity:** Dates should be very close, ideally the same day or within a 2-3 day window.
        3.  **Aggregations & Splits:** 
            - A single Crimson transaction may match a sum of multiple Bank transactions.
            - A single Bank deposit (positive amount) may be the sum of multiple Crimson contributions.
            - A single bank payout (like 'WINRED PAYOUT') may correspond to multiple Crimson entries (e.g., a gross receipt, a chargeback, and fees).
        4.  **Description:** Use keywords in the bank description to help identify matches. E.g., 'DEPOSIT' links to contributions/receipts, 'NSF' or 'CHGBK' to chargebacks.

        Return an array of matched pairs in the specified JSON schema. Only return high-confidence matches (confidenceScore > 0.85). If no matches are found, return an empty array.
        For 'bankTransactionId', ALWAYS return an array of strings, even if there's only one matching bank transaction.

        Here is the data:
        \`\`\`json
        {
            "crimsonTransactions": ${JSON.stringify(crimsonTransactions.map(({batchDetails, ...rest}) => rest))},
            "bankTransactions": ${JSON.stringify(bankTransactions.map(({splitDetails, ...rest}) => rest))}
        }
        \`\`\`
    `;

    try {
        const response = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                responseSchema: responseSchema,
                temperature: 0.1,
            },
        });
        
        const jsonText = response.text?.trim();
        if (!jsonText) {
             console.log("Gemini returned an empty or invalid response.");
             return [];
        }
        // Basic validation
        if (!jsonText.startsWith('[') || !jsonText.endsWith(']')) {
            console.error("Gemini response is not a valid JSON array:", jsonText);
            return [];
        }

        const matchedPairs = JSON.parse(jsonText) as MatchedPair[];
        return matchedPairs.filter(p => p.confidenceScore > 0.85 && p.bankTransactionId.length > 0);

    } catch (error) {
        console.error("Error calling or parsing Gemini API response:", error);
        throw new Error("Failed to get reconciliation suggestions from AI.");
    }
};