import { CrimsonTransaction, BankTransaction, MoneyType } from './types';

export const initialCrimsonTransactions: CrimsonTransaction[] = [
    { id: 'C1', date: '4/1/21', moneyType: MoneyType.Contribution, paymentType: 'CH', amount: 225, group: '225 X', isReconciled: false, batchDetails: [
        { id: 'D1', amount: 200, donor: '<PERSON>or A' },
        { id: 'D2', amount: 25, donor: '<PERSON><PERSON> B' },
    ] },
    { id: 'C2', date: '4/1/21', moneyType: MoneyType.Contribution, paymentType: 'CA', amount: 81, group: '', isReconciled: false },
    { id: 'C3', date: '4/2/21', moneyType: MoneyType.OtherReceipt, paymentType: 'CH', amount: 74736.01, group: '', isReconciled: true },
    { id: 'C4', date: '4/5/21', moneyType: MoneyType.OtherReceipt, paymentType: 'CH', amount: 1680, group: '', isReconciled: false },
    { id: 'C5', date: '4/6/21', moneyType: MoneyType.OtherReceipt, paymentType: 'CH', amount: 1865, group: '', isReconciled: false },
    { id: 'C6', date: '4/7/21', moneyType: MoneyType.Debit, paymentType: 'CH-Debit', amount: -25, group: '', isReconciled: false },
    { id: 'C7', date: '4/9/21', moneyType: MoneyType.Winred, paymentType: 'WR', amount: 975, group: '833.55', isReconciled: true },
    { id: 'C8', date: '4/9/21', moneyType: MoneyType.WinredChargeback, paymentType: 'WR-Chargeback', amount: -100, group: '833.55', isReconciled: true },
    { id: 'C9', date: '4/9/21', moneyType: MoneyType.Disbursement, paymentType: 'Fees', amount: -41.45, group: '833.55', isReconciled: true },
];

export const initialBankTransactions: BankTransaction[] = [
    { id: 'B1', date: '4/1/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 200, isReconciled: false },
    { id: 'B2', date: '4/1/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 25, isReconciled: false },
    { id: 'B3', date: '4/1/21', description: 'DDA REGULAR DEPOSIT', amount: 81, isReconciled: false },
    { id: 'B4', date: '4/2/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 1515, isReconciled: false },
    { id: 'B5', date: '4/2/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 73221.01, isReconciled: true },
    { id: 'B6', date: '4/5/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 1680, isReconciled: false },
    { id: 'B7', date: '4/6/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 1865, isReconciled: false },
    { id: 'B8', date: '4/7/21', description: 'DDA IMAGE CASH LETTER DEPOSIT', amount: 175, isReconciled: false },
    { id: 'B9', date: '4/7/21', description: 'DOROTHY HANEY CHGBK NSF 1029', amount: -25, isReconciled: false },
    { id: 'B10', date: '4/7/21', description: 'TRANSFER FROM X2171 TO X1975', amount: -50000, isReconciled: false },
    { id: 'B11', date: '4/9/21', description: 'WINRED PAYOUT', amount: 833.55, isReconciled: true, splitDetails: { gross: 975, chargebacks: -100, fees: -41.45 } },
];