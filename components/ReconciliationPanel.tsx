import React from 'react';
import { AnyTransaction, ReconciliationStatus, TransactionType, MatchedPair, BankTransaction } from '../types';
import { TransactionTable } from './TransactionTable';
import { FilterBar, FilterOptions } from './FilterBar';
import { PlusIcon, UploadIcon } from './Icons';

interface ReconciliationPanelProps {
    title: string;
    transactions: AnyTransaction[];
    transactionType: TransactionType;
    statusFilter: ReconciliationStatus;
    onStatusFilterChange: (status: ReconciliationStatus) => void;
    selectedIds: Set<string>;
    onSelectionChange: React.Dispatch<React.SetStateAction<Set<string>>>;
    aiSuggestions: MatchedPair[];
    expandedRows: Set<string>;
    onToggleExpand: React.Dispatch<React.SetStateAction<Set<string>>>;
    onOpenImportModal?: () => void;
    onOpenSplitModal?: (transaction: BankTransaction) => void;
    onMarkAsNrit?: (transactionId: string) => void;
    onUnmarkAsNrit?: (transactionId: string) => void;
    filters: FilterOptions;
    onFiltersChange: (filters: FilterOptions) => void;
}

export const ReconciliationPanel: React.FC<ReconciliationPanelProps> = ({
    title,
    transactions,
    transactionType,
    statusFilter,
    onStatusFilterChange,
    selectedIds,
    onSelectionChange,
    aiSuggestions,
    expandedRows,
    onToggleExpand,
    onOpenImportModal,
    onOpenSplitModal,
    onMarkAsNrit,
    onUnmarkAsNrit,
    filters,
    onFiltersChange,
}) => {
    const isCrimson = transactionType === TransactionType.CRIMSON;

    return (
        <div className="bg-white rounded-xl shadow-xl p-4 sm:p-6 flex flex-col h-full min-h-0 border border-slate-200/50 overflow-hidden">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
                <h2 className="text-xl font-bold text-slate-800">{title}</h2>
                <div className="flex flex-wrap items-center gap-2">
                    {isCrimson ? (
                        <>
                            <button className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-sky-600 rounded-md hover:bg-sky-700 transition-colors shadow whitespace-nowrap">
                                <PlusIcon className="w-4 h-4" /> Create Receipt
                            </button>
                            <button className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-rose-600 rounded-md hover:bg-rose-700 transition-colors shadow whitespace-nowrap">
                                <PlusIcon className="w-4 h-4" /> Create Expense
                            </button>
                        </>
                    ) : (
                        <button
                            onClick={onOpenImportModal}
                            className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 transition-colors shadow whitespace-nowrap">
                           <UploadIcon className="w-4 h-4" /> Import Activity
                        </button>
                    )}
                </div>
            </div>

            <div className="flex-shrink-0 mb-4">
                <FilterBar
                    statusFilter={statusFilter}
                    onStatusFilterChange={onStatusFilterChange}
                    isCrimson={isCrimson}
                    filters={filters}
                    onFiltersChange={onFiltersChange}
                />
            </div>

            <div className="flex-grow overflow-hidden relative min-h-0">
                 <TransactionTable
                    transactions={transactions}
                    transactionType={transactionType}
                    selectedIds={selectedIds}
                    onSelectionChange={onSelectionChange}
                    aiSuggestions={aiSuggestions}
                    expandedRows={expandedRows}
                    onToggleExpand={onToggleExpand}
                    onOpenSplitModal={onOpenSplitModal}
                    onMarkAsNrit={onMarkAsNrit}
                    onUnmarkAsNrit={onUnmarkAsNrit}
                />
            </div>
        </div>
    );
};