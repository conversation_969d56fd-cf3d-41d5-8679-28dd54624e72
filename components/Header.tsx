import React from 'react';
import { CogIcon, UploadIcon } from './Icons';

export const Header: React.FC = () => {
    return (
        <header className="bg-white shadow-sm border-b border-slate-200">
            <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <div className="flex items-center">
                        <span className="font-extrabold text-xl text-slate-800">CRIMSON</span>
                        <span className="text-xl text-slate-500 ml-2 font-medium">Treasury</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <button className="p-2 rounded-full text-slate-500 hover:bg-slate-100 hover:text-slate-700 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                        </button>
                         <button className="p-2 rounded-full text-slate-500 hover:bg-slate-100 hover:text-slate-700 transition-colors">
                            <UploadIcon className="h-6 w-6" />
                        </button>
                        <button className="p-2 rounded-full text-slate-500 hover:bg-slate-100 hover:text-slate-700 transition-colors">
                            <CogIcon className="h-6 w-6"/>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    );
};