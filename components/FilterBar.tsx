
import React, { useState } from 'react';
import { ReconciliationStatus } from '../types';

interface FilterBarProps {
    statusFilter: ReconciliationStatus;
    onStatusFilterChange: (status: ReconciliationStatus) => void;
    isCrimson: boolean;
}

const FilterInputWrapper: React.FC<{label: string; htmlFor: string; children: React.ReactNode}> = ({label, htmlFor, children}) => (
    <div>
        <label htmlFor={htmlFor} className="text-xs font-semibold text-slate-500 uppercase tracking-wider">
            {label}
        </label>
        {children}
    </div>
);


export const FilterBar: React.FC<FilterBarProps> = ({ statusFilter, onStatusFilterChange, isCrimson }) => {
    const [selectedFundCode, setSelectedFundCode] = useState('All');

    const commonSelectClass = "w-full mt-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm bg-white";
    const commonInputClass = "w-full mt-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm";
    
    return (
        <div className="bg-slate-50 p-3 rounded-lg border border-slate-200/80">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <FilterInputWrapper label="Display" htmlFor="filter-display">
                     <div className="flex mt-1">
                        <button
                            id="filter-display"
                            onClick={() => onStatusFilterChange(ReconciliationStatus.Unreconciled)}
                            className={`px-3 py-1.5 text-sm w-full rounded-l-md transition-colors ${statusFilter === ReconciliationStatus.Unreconciled ? 'bg-sky-600 text-white font-semibold' : 'bg-white text-slate-700 hover:bg-slate-100 border border-slate-300'}`}
                        >
                            Unreconciled
                        </button>
                        <button
                            onClick={() => onStatusFilterChange(ReconciliationStatus.All)}
                            className={`px-3 py-1.5 text-sm w-full rounded-r-md transition-colors ${statusFilter === ReconciliationStatus.All ? 'bg-sky-600 text-white font-semibold' : 'bg-white text-slate-700 hover:bg-slate-100 border border-r-slate-300 border-t-slate-300 border-b-slate-300'}`}
                        >
                            All
                        </button>
                    </div>
                </FilterInputWrapper>

                <FilterInputWrapper label={isCrimson ? 'Fund Code' : 'Credit/Debit'} htmlFor="filter-fund-code">
                    <select 
                        id="filter-fund-code" 
                        className={commonSelectClass}
                        value={selectedFundCode}
                        onChange={(e) => { if(isCrimson) setSelectedFundCode(e.target.value) }}
                    >
                        <option value="All">All</option>
                        {isCrimson ? (
                           <>
                            <option value="P2026">P2026</option>
                            <option value="G2026">G2026</option>
                            <option value="PAC">PAC</option>
                            <option value="JFC">JFC</option>
                           </>
                        ) : (
                           <>
                            <option value="Credit">Credit</option>
                            <option value="Debit">Debit</option>
                           </>
                        )}
                    </select>
                </FilterInputWrapper>

                <FilterInputWrapper label="Account Code" htmlFor="filter-account-code">
                    <select id="filter-account-code" className={commonSelectClass}>
                        <option>All</option>
                        <option>Operating P2026</option>
                        <option>Operating G2026</option>
                        <option>Operating PAC</option>
                        <option>Savings 2026</option>
                    </select>
                </FilterInputWrapper>

                 <FilterInputWrapper label={isCrimson ? 'Payment Type' : 'Description'} htmlFor="filter-payment-type">
                     {isCrimson ? (
                        <select id="filter-payment-type" className={commonSelectClass}>
                           <option>All</option>
                           <option>CH</option>
                           <option>CC</option>
                           <option>JF</option>
                           <option>IK</option>
                           <option>OT</option>
                           <option>WR</option>
                           <option>AN</option>
                        </select>
                     ) : (
                        <input type="text" id="filter-payment-type" placeholder="Filter by text..." className={commonInputClass}/>
                     )}
                </FilterInputWrapper>
            </div>

            {isCrimson && selectedFundCode !== 'All' && (
                <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="lg:col-start-2">
                        <FilterInputWrapper label="Line Number" htmlFor="filter-line-number">
                            <select id="filter-line-number" className={commonSelectClass}>
                                <option>All</option>
                                <option>SA11A - Contributions</option>
                                <option>SA17 - Refunds</option>
                                <option>SB21B - Operating Expenditures</option>
                                <option>SB23 - Contributions to Cands</option>
                                <option>SB29 - JFC Transfers Out</option>
                            </select>
                        </FilterInputWrapper>
                    </div>
                </div>
            )}
        </div>
    );
};
