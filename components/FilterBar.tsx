
import React, { useState } from 'react';
import { ReconciliationStatus } from '../types';
import { MagnifyingGlassIcon, XMarkIcon, CalendarIcon, CurrencyDollarIcon } from './Icons';

export interface FilterOptions {
    statusFilter: ReconciliationStatus;
    searchText: string;
    dateRange: { start: string; end: string };
    amountRange: { min: number | null; max: number | null };
    fundCode: string;
    accountCode: string;
    paymentType: string;
    showAdvanced: boolean;
}

interface FilterBarProps {
    statusFilter: ReconciliationStatus;
    onStatusFilterChange: (status: ReconciliationStatus) => void;
    isCrimson: boolean;
    filters: FilterOptions;
    onFiltersChange: (filters: FilterOptions) => void;
}

const FilterInputWrapper: React.FC<{label: string; htmlFor: string; children: React.ReactNode}> = ({label, htmlFor, children}) => (
    <div className="min-w-0 flex-1">
        <label htmlFor={htmlFor} className="block text-xs font-semibold text-slate-500 uppercase tracking-wider mb-1 truncate">
            {label}
        </label>
        {children}
    </div>
);


export const FilterBar: React.FC<FilterBarProps> = ({ statusFilter, onStatusFilterChange, isCrimson, filters, onFiltersChange }) => {
    const commonSelectClass = "w-full mt-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm bg-white";
    const commonInputClass = "w-full mt-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm";

    const updateFilter = (key: keyof FilterOptions, value: any) => {
        onFiltersChange({ ...filters, [key]: value });
    };

    const clearAllFilters = () => {
        onFiltersChange({
            statusFilter: ReconciliationStatus.Unreconciled,
            searchText: '',
            dateRange: { start: '', end: '' },
            amountRange: { min: null, max: null },
            fundCode: 'All',
            accountCode: 'All',
            paymentType: 'All',
            showAdvanced: false,
        });
        onStatusFilterChange(ReconciliationStatus.Unreconciled);
    };

    const hasActiveFilters = filters.searchText ||
        filters.dateRange.start ||
        filters.dateRange.end ||
        filters.amountRange.min !== null ||
        filters.amountRange.max !== null ||
        filters.fundCode !== 'All' ||
        filters.accountCode !== 'All' ||
        filters.paymentType !== 'All';

    return (
        <div className="bg-slate-50 p-3 rounded-lg border border-slate-200/80 overflow-hidden">
            {/* Main Search Bar */}
            <div className="mb-3">
                <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                    <input
                        type="text"
                        placeholder="Search transactions by description, amount, or date..."
                        value={filters.searchText}
                        onChange={(e) => updateFilter('searchText', e.target.value)}
                        className="w-full pl-10 pr-10 py-2.5 border border-slate-300 rounded-lg shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm"
                    />
                    {filters.searchText && (
                        <button
                            onClick={() => updateFilter('searchText', '')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                        >
                            <XMarkIcon className="w-5 h-5" />
                        </button>
                    )}
                </div>
            </div>

            {/* Primary Filters */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 mb-3">
                <FilterInputWrapper label="Display" htmlFor="filter-display">
                     <div className="flex mt-1">
                        <button
                            id="filter-display"
                            onClick={() => onStatusFilterChange(ReconciliationStatus.Unreconciled)}
                            className={`px-3 py-1.5 text-sm w-full rounded-l-md transition-colors ${statusFilter === ReconciliationStatus.Unreconciled ? 'bg-sky-600 text-white font-semibold' : 'bg-white text-slate-700 hover:bg-slate-100 border border-slate-300'}`}
                        >
                            Unreconciled
                        </button>
                        <button
                            onClick={() => onStatusFilterChange(ReconciliationStatus.All)}
                            className={`px-3 py-1.5 text-sm w-full rounded-r-md transition-colors ${statusFilter === ReconciliationStatus.All ? 'bg-sky-600 text-white font-semibold' : 'bg-white text-slate-700 hover:bg-slate-100 border border-r-slate-300 border-t-slate-300 border-b-slate-300'}`}
                        >
                            All
                        </button>
                    </div>
                </FilterInputWrapper>

                <FilterInputWrapper label="Date Range" htmlFor="filter-date-start">
                    <div className="flex gap-2 mt-1">
                        <input
                            type="date"
                            id="filter-date-start"
                            value={filters.dateRange.start}
                            onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, start: e.target.value })}
                            className="flex-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm"
                        />
                        <input
                            type="date"
                            value={filters.dateRange.end}
                            onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, end: e.target.value })}
                            className="flex-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm"
                        />
                    </div>
                </FilterInputWrapper>

                <FilterInputWrapper label="Amount Range" htmlFor="filter-amount-min">
                    <div className="flex gap-2 mt-1">
                        <input
                            type="number"
                            id="filter-amount-min"
                            placeholder="Min"
                            value={filters.amountRange.min || ''}
                            onChange={(e) => updateFilter('amountRange', { ...filters.amountRange, min: e.target.value ? parseFloat(e.target.value) : null })}
                            className="flex-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm"
                        />
                        <input
                            type="number"
                            placeholder="Max"
                            value={filters.amountRange.max || ''}
                            onChange={(e) => updateFilter('amountRange', { ...filters.amountRange, max: e.target.value ? parseFloat(e.target.value) : null })}
                            className="flex-1 p-1.5 border border-slate-300 rounded-md shadow-sm focus:ring-sky-500 focus:border-sky-500 text-sm"
                        />
                    </div>
                </FilterInputWrapper>

            </div>

            {/* Filter Actions */}
            <div className="flex flex-wrap items-center justify-between gap-2 mb-3">
                <div className="flex flex-wrap items-center gap-2">
                    <button
                        onClick={() => updateFilter('showAdvanced', !filters.showAdvanced)}
                        className="px-3 py-1.5 text-sm font-medium text-sky-600 hover:text-sky-800 hover:bg-sky-50 rounded-md transition-colors whitespace-nowrap"
                    >
                        {filters.showAdvanced ? 'Hide' : 'More'} Filters
                    </button>
                    {hasActiveFilters && (
                        <button
                            onClick={clearAllFilters}
                            className="px-3 py-1.5 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-md transition-colors flex items-center gap-1 whitespace-nowrap"
                        >
                            <XMarkIcon className="w-4 h-4" />
                            Clear
                        </button>
                    )}
                </div>
            </div>

            {/* Advanced Filters */}
            {filters.showAdvanced && (
                <div className="border-t border-slate-200 pt-3">
                    <div className="grid grid-cols-1 gap-3">
                        <FilterInputWrapper label={isCrimson ? 'Fund Code' : 'Transaction Type'} htmlFor="filter-fund-code">
                            <select
                                id="filter-fund-code"
                                className={commonSelectClass}
                                value={filters.fundCode}
                                onChange={(e) => updateFilter('fundCode', e.target.value)}
                            >
                                <option value="All">All</option>
                                {isCrimson ? (
                                   <>
                                    <option value="P2026">P2026</option>
                                    <option value="G2026">G2026</option>
                                    <option value="PAC">PAC</option>
                                    <option value="JFC">JFC</option>
                                   </>
                                ) : (
                                   <>
                                    <option value="Credit">Credit Only</option>
                                    <option value="Debit">Debit Only</option>
                                   </>
                                )}
                            </select>
                        </FilterInputWrapper>

                        <FilterInputWrapper label="Account Code" htmlFor="filter-account-code">
                            <select
                                id="filter-account-code"
                                className={commonSelectClass}
                                value={filters.accountCode}
                                onChange={(e) => updateFilter('accountCode', e.target.value)}
                            >
                                <option value="All">All</option>
                                <option value="Operating P2026">Operating P2026</option>
                                <option value="Operating G2026">Operating G2026</option>
                                <option value="Operating PAC">Operating PAC</option>
                                <option value="Savings 2026">Savings 2026</option>
                            </select>
                        </FilterInputWrapper>

                         <FilterInputWrapper label={isCrimson ? 'Payment Type' : 'Description Contains'} htmlFor="filter-payment-type">
                             {isCrimson ? (
                                <select
                                    id="filter-payment-type"
                                    className={commonSelectClass}
                                    value={filters.paymentType}
                                    onChange={(e) => updateFilter('paymentType', e.target.value)}
                                >
                                   <option value="All">All</option>
                                   <option value="CH">CH - Check</option>
                                   <option value="CC">CC - Credit Card</option>
                                   <option value="JF">JF - Joint Fundraising</option>
                                   <option value="IK">IK - In-Kind</option>
                                   <option value="OT">OT - Other</option>
                                   <option value="WR">WR - WinRed</option>
                                   <option value="AN">AN - ActBlue</option>
                                </select>
                             ) : (
                                <input
                                    type="text"
                                    id="filter-payment-type"
                                    placeholder="e.g., DEPOSIT, TRANSFER, PAYOUT..."
                                    className={commonInputClass}
                                    value={filters.paymentType === 'All' ? '' : filters.paymentType}
                                    onChange={(e) => updateFilter('paymentType', e.target.value || 'All')}
                                />
                             )}
                        </FilterInputWrapper>
                    </div>
                </div>
            )}

            {/* Active Filters Summary */}
            {hasActiveFilters && (
                <div className="mt-4 pt-3 border-t border-slate-200">
                    <div className="flex flex-wrap gap-2 items-center">
                        <span className="text-xs font-semibold text-slate-500 uppercase tracking-wider">Active Filters:</span>
                        {filters.searchText && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-sky-100 text-sky-800 text-xs rounded-full">
                                Search: "{filters.searchText}"
                                <button onClick={() => updateFilter('searchText', '')} className="hover:text-sky-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {(filters.dateRange.start || filters.dateRange.end) && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-emerald-100 text-emerald-800 text-xs rounded-full">
                                Date: {filters.dateRange.start || '...'} to {filters.dateRange.end || '...'}
                                <button onClick={() => updateFilter('dateRange', { start: '', end: '' })} className="hover:text-emerald-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                        {(filters.amountRange.min !== null || filters.amountRange.max !== null) && (
                            <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                                Amount: ${filters.amountRange.min || '...'} - ${filters.amountRange.max || '...'}
                                <button onClick={() => updateFilter('amountRange', { min: null, max: null })} className="hover:text-purple-600">
                                    <XMarkIcon className="w-3 h-3" />
                                </button>
                            </span>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};
