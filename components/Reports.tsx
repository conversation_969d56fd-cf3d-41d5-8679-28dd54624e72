import React, { useState, useMemo } from 'react';
import { 
    DocumentTextIcon, 
    ChartBarIcon, 
    BanknotesIcon, 
    ExclamationTriangleIcon,
    ShieldCheckIcon,
    ClockIcon,
    CurrencyDollarIcon,
    UserGroupIcon,
    BuildingOfficeIcon,
    DocumentArrowDownIcon,
    CalendarIcon,
    FunnelIcon
} from './Icons';
import { ReconciliationSession, ReportType, ReportParameters } from '../types';

interface ReportsProps {
    sessions: ReconciliationSession[];
    onGenerateReport: (type: ReportType, parameters: ReportParameters) => void;
    onExportReport: (reportId: string, format: 'pdf' | 'excel' | 'csv') => void;
}

interface ReportDefinition {
    type: ReportType;
    name: string;
    description: string;
    icon: React.ReactElement;
    category: 'core' | 'compliance' | 'insights';
    color: string;
    estimatedTime: string;
    useCase: string;
}

const REPORT_DEFINITIONS: ReportDefinition[] = [
    // Core Reconciliation Reports
    {
        type: ReportType.RECONCILIATION_SUMMARY,
        name: 'Reconciliation Summary',
        description: 'High-level overview of reconciliation status and key metrics',
        icon: <ChartBarIcon className="w-6 h-6" />,
        category: 'core',
        color: 'bg-blue-500',
        estimatedTime: '< 1 min',
        useCase: 'Executive summary for treasurers and senior staff'
    },
    {
        type: ReportType.FUND_BY_FUND,
        name: 'Fund-by-Fund Reconciliation',
        description: 'Detailed breakdown by political fund (P2026, G2026, PAC, JFC)',
        icon: <BanknotesIcon className="w-6 h-6" />,
        category: 'core',
        color: 'bg-green-500',
        estimatedTime: '2-3 min',
        useCase: 'Fund-specific analysis and compliance review'
    },
    {
        type: ReportType.FEC_LINE_ITEM,
        name: 'FEC Line Item Reconciliation',
        description: 'Compliance-focused report by FEC line numbers (SA11A, SB21B, etc.)',
        icon: <DocumentTextIcon className="w-6 h-6" />,
        category: 'core',
        color: 'bg-purple-500',
        estimatedTime: '2-3 min',
        useCase: 'FEC filing preparation and compliance verification'
    },
    {
        type: ReportType.BANK_ACCOUNT_REC,
        name: 'Bank Account Reconciliation',
        description: 'Traditional bank reconciliation with political context',
        icon: <BuildingOfficeIcon className="w-6 h-6" />,
        category: 'core',
        color: 'bg-indigo-500',
        estimatedTime: '1-2 min',
        useCase: 'Monthly bank reconciliation and cash management'
    },
    
    // Compliance & Audit Reports
    {
        type: ReportType.DISCREPANCY_ANALYSIS,
        name: 'Discrepancy Analysis',
        description: 'Detailed analysis of reconciliation issues and resolutions',
        icon: <ExclamationTriangleIcon className="w-6 h-6" />,
        category: 'compliance',
        color: 'bg-yellow-500',
        estimatedTime: '3-5 min',
        useCase: 'Issue tracking and resolution documentation'
    },
    {
        type: ReportType.COMPLIANCE_CERTIFICATION,
        name: 'Compliance Certification',
        description: 'Formal certification report for FEC filing and audit purposes',
        icon: <ShieldCheckIcon className="w-6 h-6" />,
        category: 'compliance',
        color: 'bg-emerald-500',
        estimatedTime: '2-3 min',
        useCase: 'Treasurer certification and audit documentation'
    },
    {
        type: ReportType.AUDIT_TRAIL,
        name: 'Audit Trail Report',
        description: 'Complete history of reconciliation actions and changes',
        icon: <ClockIcon className="w-6 h-6" />,
        category: 'compliance',
        color: 'bg-slate-500',
        estimatedTime: '1-2 min',
        useCase: 'Audit support and internal control documentation'
    },
    
    // Value-Added Insights
    {
        type: ReportType.CASH_FLOW_BY_FUND,
        name: 'Cash Flow by Fund',
        description: 'Strategic cash position analysis and projections',
        icon: <CurrencyDollarIcon className="w-6 h-6" />,
        category: 'insights',
        color: 'bg-teal-500',
        estimatedTime: '2-4 min',
        useCase: 'Strategic financial planning and cash management'
    },
    {
        type: ReportType.CONTRIBUTION_ANALYSIS,
        name: 'Contribution Analysis',
        description: 'Fundraising performance insights and trends',
        icon: <UserGroupIcon className="w-6 h-6" />,
        category: 'insights',
        color: 'bg-rose-500',
        estimatedTime: '3-5 min',
        useCase: 'Fundraising strategy and performance analysis'
    },
    {
        type: ReportType.VENDOR_PAYMENT_REC,
        name: 'Vendor Payment Reconciliation',
        description: 'Expenditure compliance and vendor payment analysis',
        icon: <BuildingOfficeIcon className="w-6 h-6" />,
        category: 'insights',
        color: 'bg-orange-500',
        estimatedTime: '2-3 min',
        useCase: 'Vendor management and expenditure compliance'
    }
];

export const Reports: React.FC<ReportsProps> = ({ sessions, onGenerateReport, onExportReport }) => {
    const [selectedSession, setSelectedSession] = useState<string>('');
    const [selectedCategory, setSelectedCategory] = useState<'all' | 'core' | 'compliance' | 'insights'>('all');
    const [reportParameters, setReportParameters] = useState<Partial<ReportParameters>>({
        detailLevel: 'detailed',
        includeResolved: true,
        includeUnresolved: true
    });

    const completedSessions = useMemo(() => {
        return sessions.filter(session => session.status === 'completed' || session.status === 'certified');
    }, [sessions]);

    const filteredReports = useMemo(() => {
        if (selectedCategory === 'all') return REPORT_DEFINITIONS;
        return REPORT_DEFINITIONS.filter(report => report.category === selectedCategory);
    }, [selectedCategory]);

    const handleGenerateReport = (reportType: ReportType) => {
        if (!selectedSession) {
            alert('Please select a reconciliation session first.');
            return;
        }

        const parameters: ReportParameters = {
            sessionId: selectedSession,
            detailLevel: reportParameters.detailLevel || 'detailed',
            includeResolved: reportParameters.includeResolved ?? true,
            includeUnresolved: reportParameters.includeUnresolved ?? true,
            ...reportParameters
        };

        onGenerateReport(reportType, parameters);
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'core': return 'text-blue-600 bg-blue-50 border-blue-200';
            case 'compliance': return 'text-emerald-600 bg-emerald-50 border-emerald-200';
            case 'insights': return 'text-purple-600 bg-purple-50 border-purple-200';
            default: return 'text-slate-600 bg-slate-50 border-slate-200';
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h2 className="text-2xl font-bold text-slate-800 flex items-center gap-3">
                        <DocumentTextIcon className="w-8 h-8 text-blue-600" />
                        Reconciliation Reports
                    </h2>
                    <p className="text-slate-600 mt-1">
                        Generate comprehensive reports for compliance, analysis, and strategic planning
                    </p>
                </div>
            </div>

            {/* Session Selection and Filters */}
            <div className="bg-slate-50 p-4 rounded-lg mb-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                            <CalendarIcon className="w-4 h-4 inline mr-1" />
                            Reconciliation Session
                        </label>
                        <select
                            value={selectedSession}
                            onChange={(e) => setSelectedSession(e.target.value)}
                            className="w-full p-2 border border-slate-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Select a session...</option>
                            {completedSessions.map(session => (
                                <option key={session.id} value={session.id}>
                                    {session.name} ({session.period.start} to {session.period.end})
                                </option>
                            ))}
                        </select>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                            <FunnelIcon className="w-4 h-4 inline mr-1" />
                            Report Category
                        </label>
                        <select
                            value={selectedCategory}
                            onChange={(e) => setSelectedCategory(e.target.value as any)}
                            className="w-full p-2 border border-slate-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="all">All Reports</option>
                            <option value="core">Core Reconciliation</option>
                            <option value="compliance">Compliance & Audit</option>
                            <option value="insights">Strategic Insights</option>
                        </select>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                            Detail Level
                        </label>
                        <select
                            value={reportParameters.detailLevel}
                            onChange={(e) => setReportParameters(prev => ({ ...prev, detailLevel: e.target.value as any }))}
                            className="w-full p-2 border border-slate-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="summary">Summary</option>
                            <option value="detailed">Detailed</option>
                            <option value="full">Full Detail</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Report Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredReports.map((report) => (
                    <div key={report.type} className="border border-slate-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-4">
                            <div className={`p-3 rounded-lg ${report.color} text-white`}>
                                {report.icon}
                            </div>
                            <span className={`px-2 py-1 text-xs font-medium rounded border ${getCategoryColor(report.category)}`}>
                                {report.category}
                            </span>
                        </div>
                        
                        <h3 className="text-lg font-semibold text-slate-800 mb-2">{report.name}</h3>
                        <p className="text-sm text-slate-600 mb-4">{report.description}</p>
                        
                        <div className="space-y-2 mb-4">
                            <div className="flex justify-between text-xs text-slate-500">
                                <span>Generation time:</span>
                                <span>{report.estimatedTime}</span>
                            </div>
                            <div className="text-xs text-slate-500">
                                <strong>Use case:</strong> {report.useCase}
                            </div>
                        </div>
                        
                        <button
                            onClick={() => handleGenerateReport(report.type)}
                            disabled={!selectedSession}
                            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                        >
                            <DocumentArrowDownIcon className="w-4 h-4 inline mr-2" />
                            Generate Report
                        </button>
                    </div>
                ))}
            </div>

            {/* Quick Actions */}
            {selectedSession && (
                <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="text-lg font-semibold text-blue-800 mb-3">Quick Actions</h3>
                    <div className="flex flex-wrap gap-2">
                        <button
                            onClick={() => handleGenerateReport(ReportType.RECONCILIATION_SUMMARY)}
                            className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                        >
                            Generate Summary
                        </button>
                        <button
                            onClick={() => handleGenerateReport(ReportType.COMPLIANCE_CERTIFICATION)}
                            className="px-3 py-1.5 bg-emerald-600 text-white rounded text-sm hover:bg-emerald-700 transition-colors"
                        >
                            Compliance Cert
                        </button>
                        <button
                            onClick={() => handleGenerateReport(ReportType.FEC_LINE_ITEM)}
                            className="px-3 py-1.5 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors"
                        >
                            FEC Line Items
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};
