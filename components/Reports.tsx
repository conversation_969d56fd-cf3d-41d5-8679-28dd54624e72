import React, { useState } from 'react';
import { 
    DocumentTextIcon, 
    ChartBarIcon, 
    ExclamationTriangleIcon,
    DocumentArrowDownIcon,
    EyeIcon,
    CheckCircleIcon,
    ClockIcon
} from './Icons';
import { completedReconciliationSessions } from '../constants';

interface ReportsProps {
    onViewReconciliation?: (sessionId: string) => void;
}

const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
});

export const Reports: React.FC<ReportsProps> = ({ onViewReconciliation }) => {
    const [activeView, setActiveView] = useState<'overview' | 'detailed' | 'discrepancies' | 'export'>('overview');
    const [selectedSession, setSelectedSession] = useState<string | null>(null);

    const sessions = completedReconciliationSessions;

    const reportTypes = [
        {
            id: 'overview',
            title: 'Reconciliation Summary',
            description: 'Overview of all completed reconciliation sessions',
            icon: <ChartBarIcon className="w-6 h-6" />,
            count: sessions.length
        },
        {
            id: 'detailed',
            title: 'Detailed Reconciliation',
            description: 'Drill-down into specific reconciliation with transaction details',
            icon: <DocumentTextIcon className="w-6 h-6" />,
            count: sessions.filter(s => s.status === 'certified').length
        },
        {
            id: 'discrepancies',
            title: 'Discrepancy Analysis',
            description: 'Focus on unmatched and problematic transactions',
            icon: <ExclamationTriangleIcon className="w-6 h-6" />,
            count: sessions.reduce((sum, s) => sum + s.discrepancies, 0)
        },
        {
            id: 'export',
            title: 'Compliance Export',
            description: 'FEC-ready export format for regulatory submissions',
            icon: <DocumentArrowDownIcon className="w-6 h-6" />,
            count: sessions.filter(s => s.status === 'certified').length
        }
    ];

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'certified':
                return <CheckCircleIcon className="w-5 h-5 text-emerald-600" />;
            case 'completed':
                return <ClockIcon className="w-5 h-5 text-blue-600" />;
            case 'in_progress':
                return <ClockIcon className="w-5 h-5 text-amber-600" />;
            default:
                return <ClockIcon className="w-5 h-5 text-slate-400" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'certified':
                return 'bg-emerald-100 text-emerald-800';
            case 'completed':
                return 'bg-blue-100 text-blue-800';
            case 'in_progress':
                return 'bg-amber-100 text-amber-800';
            default:
                return 'bg-slate-100 text-slate-800';
        }
    };

    const renderOverviewReport = () => (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-white p-4 rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-slate-800">{sessions.length}</div>
                    <div className="text-sm text-slate-600">Total Sessions</div>
                </div>
                <div className="bg-white p-4 rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-emerald-600">{sessions.filter(s => s.status === 'certified').length}</div>
                    <div className="text-sm text-slate-600">Certified</div>
                </div>
                <div className="bg-white p-4 rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-blue-600">{sessions.filter(s => s.status === 'completed').length}</div>
                    <div className="text-sm text-slate-600">Completed</div>
                </div>
                <div className="bg-white p-4 rounded-lg border border-slate-200">
                    <div className="text-2xl font-bold text-amber-600">{sessions.reduce((sum, s) => sum + s.discrepancies, 0)}</div>
                    <div className="text-sm text-slate-600">Total Discrepancies</div>
                </div>
            </div>

            <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
                <div className="px-6 py-4 border-b border-slate-200">
                    <h3 className="text-lg font-semibold text-slate-800">Reconciliation Sessions</h3>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-slate-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Session</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Period</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Transactions</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Amount</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Discrepancies</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-slate-200">
                            {sessions.map((session) => (
                                <tr key={session.id} className="hover:bg-slate-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div className="text-sm font-medium text-slate-900">{session.name}</div>
                                            <div className="text-sm text-slate-500">by {session.user}</div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                        {formatDate(session.startDate)} - {formatDate(session.endDate)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center gap-2">
                                            {getStatusIcon(session.status)}
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(session.status)}`}>
                                                {session.status.replace('_', ' ').toUpperCase()}
                                            </span>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                        {session.reconciledTransactions}/{session.totalCrimsonTransactions + session.totalBankTransactions}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                        {formatCurrency(session.totalCrimsonAmount)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                            session.discrepancies === 0 ? 'bg-emerald-100 text-emerald-800' : 'bg-rose-100 text-rose-800'
                                        }`}>
                                            {session.discrepancies}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button
                                            onClick={() => {
                                                setSelectedSession(session.id);
                                                setActiveView('detailed');
                                            }}
                                            className="text-sky-600 hover:text-sky-900 flex items-center gap-1"
                                        >
                                            <EyeIcon className="w-4 h-4" />
                                            View Details
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );

    const renderDetailedReport = () => {
        const session = sessions.find(s => s.id === selectedSession);
        if (!session) return null;

        return (
            <div className="space-y-6">
                {/* Back Button */}
                <button
                    onClick={() => {
                        setSelectedSession(null);
                        setActiveView('overview');
                    }}
                    className="flex items-center gap-2 text-sky-600 hover:text-sky-800 font-medium"
                >
                    ← Back to Overview
                </button>

                {/* Session Header */}
                <div className="bg-white p-6 rounded-lg border border-slate-200">
                    <div className="flex items-center justify-between mb-4">
                        <div>
                            <h3 className="text-xl font-bold text-slate-800">{session.name}</h3>
                            <p className="text-slate-600">
                                {formatDate(session.startDate)} - {formatDate(session.endDate)} • by {session.user}
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            {getStatusIcon(session.status)}
                            <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(session.status)}`}>
                                {session.status.replace('_', ' ').toUpperCase()}
                            </span>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-slate-50 rounded-lg">
                            <div className="text-2xl font-bold text-slate-800">{session.totalCrimsonTransactions}</div>
                            <div className="text-sm text-slate-600">Crimson Transactions</div>
                        </div>
                        <div className="text-center p-3 bg-slate-50 rounded-lg">
                            <div className="text-2xl font-bold text-slate-800">{session.totalBankTransactions}</div>
                            <div className="text-sm text-slate-600">Bank Transactions</div>
                        </div>
                        <div className="text-center p-3 bg-emerald-50 rounded-lg">
                            <div className="text-2xl font-bold text-emerald-600">{session.reconciledTransactions}</div>
                            <div className="text-sm text-slate-600">Reconciled</div>
                        </div>
                        <div className="text-center p-3 bg-rose-50 rounded-lg">
                            <div className="text-2xl font-bold text-rose-600">{session.discrepancies}</div>
                            <div className="text-sm text-slate-600">Discrepancies</div>
                        </div>
                    </div>

                    {session.notes && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-blue-800">{session.notes}</p>
                        </div>
                    )}
                </div>

                {/* Crimson Transactions */}
                <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
                    <div className="px-6 py-4 border-b border-slate-200 bg-slate-50">
                        <h4 className="text-lg font-semibold text-slate-800">Crimson Activity</h4>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-slate-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Date</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Contributor</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Amount</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Fund</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Line #</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-slate-200">
                                {session.crimsonTransactions.map((transaction) => (
                                    <tr key={transaction.id} className="hover:bg-slate-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            {formatDate(transaction.date)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-slate-900">{transaction.contributor}</div>
                                            <div className="text-sm text-slate-500">{transaction.paymentType}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            <span className={transaction.amount < 0 ? 'text-rose-600' : 'text-emerald-600'}>
                                                {formatCurrency(transaction.amount)}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            {transaction.fundCode}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                                                {transaction.lineNumber}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            {transaction.isReconciled ? (
                                                <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800">
                                                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                                                    Reconciled
                                                </span>
                                            ) : (
                                                <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-amber-100 text-amber-800">
                                                    <ClockIcon className="w-3 h-3 mr-1" />
                                                    Unreconciled
                                                </span>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Bank Transactions */}
                <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
                    <div className="px-6 py-4 border-b border-slate-200 bg-slate-50">
                        <h4 className="text-lg font-semibold text-slate-800">Bank Activity</h4>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-slate-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Date</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Description</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Amount</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Account</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-slate-200">
                                {session.bankTransactions.map((transaction) => (
                                    <tr key={transaction.id} className="hover:bg-slate-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            {formatDate(transaction.date)}
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm font-medium text-slate-900">{transaction.description}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            <span className={transaction.amount < 0 ? 'text-rose-600' : 'text-emerald-600'}>
                                                {formatCurrency(transaction.amount)}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                                            {transaction.accountCode}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            {transaction.isReconciled ? (
                                                <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800">
                                                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                                                    Reconciled
                                                </span>
                                            ) : (
                                                <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-amber-100 text-amber-800">
                                                    <ClockIcon className="w-3 h-3 mr-1" />
                                                    Unreconciled
                                                </span>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-slate-800">Reconciliation Reports</h2>
                    <p className="text-slate-600 mt-1">View completed reconciliation sessions and generate reports</p>
                </div>
            </div>

            {/* Report Type Selector */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {reportTypes.map((report) => (
                    <button
                        key={report.id}
                        onClick={() => setActiveView(report.id as any)}
                        className={`p-4 rounded-lg border-2 transition-all ${
                            activeView === report.id
                                ? 'border-sky-500 bg-sky-50 text-sky-700'
                                : 'border-slate-200 bg-white text-slate-600 hover:border-slate-300'
                        }`}
                    >
                        <div className="flex items-center gap-3 mb-2">
                            <div className={`p-2 rounded-lg ${activeView === report.id ? 'bg-sky-100' : 'bg-slate-100'}`}>
                                {report.icon}
                            </div>
                            <div className="text-left">
                                <div className="font-semibold">{report.title}</div>
                                <div className="text-sm opacity-75">{report.count} items</div>
                            </div>
                        </div>
                        <p className="text-sm text-left">{report.description}</p>
                    </button>
                ))}
            </div>

            {/* Report Content */}
            <div className="bg-slate-50 rounded-lg p-6">
                {activeView === 'overview' && renderOverviewReport()}
                {activeView === 'detailed' && selectedSession && renderDetailedReport()}
                {activeView === 'detailed' && !selectedSession && (
                    <div className="text-center py-8">
                        <DocumentTextIcon className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-slate-800 mb-2">Detailed Reconciliation Report</h3>
                        <p className="text-slate-600">Select a session from the overview to view detailed transaction-level reports</p>
                    </div>
                )}
                {activeView === 'discrepancies' && (
                    <div className="text-center py-8">
                        <ExclamationTriangleIcon className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-slate-800 mb-2">Discrepancy Analysis</h3>
                        <p className="text-slate-600">Analysis of unmatched transactions and reconciliation issues</p>
                    </div>
                )}
                {activeView === 'export' && (
                    <div className="text-center py-8">
                        <DocumentArrowDownIcon className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-slate-800 mb-2">Compliance Export</h3>
                        <p className="text-slate-600">Export reconciliation data in FEC-compliant formats</p>
                    </div>
                )}
            </div>
        </div>
    );
};
