import React, { useMemo } from 'react';
import { CrimsonTransaction, BankTransaction, ReconciliationStats, MatchedPair } from '../types';
import { ChartBarIcon, ClockIcon, CheckCircleIcon, ExclamationTriangleIcon, CurrencyDollarIcon, TrendingUpIcon, ArchiveBoxIcon, SparklesIcon } from './Icons';

interface DashboardProps {
    crimsonTransactions: CrimsonTransaction[];
    bankTransactions: BankTransaction[];
    aiSuggestions: MatchedPair[];
}

const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
const formatPercentage = (value: number) => `${Math.round(value)}%`;

const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ReactNode;
    color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
    trend?: { value: number; isPositive: boolean };
}> = ({ title, value, subtitle, icon, color, trend }) => {
    const colorClasses = {
        blue: 'bg-blue-50 border-blue-200 text-blue-700',
        green: 'bg-emerald-50 border-emerald-200 text-emerald-700',
        yellow: 'bg-yellow-50 border-yellow-200 text-yellow-700',
        red: 'bg-rose-50 border-rose-200 text-rose-700',
        purple: 'bg-purple-50 border-purple-200 text-purple-700',
        indigo: 'bg-indigo-50 border-indigo-200 text-indigo-700',
    };

    const iconColorClasses = {
        blue: 'text-blue-600',
        green: 'text-emerald-600',
        yellow: 'text-yellow-600',
        red: 'text-rose-600',
        purple: 'text-purple-600',
        indigo: 'text-indigo-600',
    };

    return (
        <div className={`p-6 rounded-xl border-2 ${colorClasses[color]} transition-all hover:shadow-lg`}>
            <div className="flex items-center justify-between">
                <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-lg bg-white ${iconColorClasses[color]}`}>
                            {icon}
                        </div>
                        <h3 className="text-sm font-semibold uppercase tracking-wider opacity-75">{title}</h3>
                    </div>
                    <p className="text-3xl font-bold mb-1">{value}</p>
                    {subtitle && <p className="text-sm opacity-75">{subtitle}</p>}
                </div>
                {trend && (
                    <div className={`flex items-center gap-1 text-sm font-medium ${trend.isPositive ? 'text-emerald-600' : 'text-rose-600'}`}>
                        <TrendingUpIcon className={`w-4 h-4 ${trend.isPositive ? '' : 'rotate-180'}`} />
                        {Math.abs(trend.value)}%
                    </div>
                )}
            </div>
        </div>
    );
};

const ProgressBar: React.FC<{ progress: number; color?: string }> = ({ progress, color = 'bg-emerald-500' }) => (
    <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
        <div 
            className={`h-full ${color} transition-all duration-500 ease-out rounded-full`}
            style={{ width: `${Math.min(progress, 100)}%` }}
        />
    </div>
);

export const Dashboard: React.FC<DashboardProps> = ({ crimsonTransactions, bankTransactions, aiSuggestions }) => {
    const stats: ReconciliationStats = useMemo(() => {
        const reconciledCrimson = crimsonTransactions.filter(t => t.isReconciled);
        const reconciledBank = bankTransactions.filter(t => t.isReconciled);
        const unreconciledCrimson = crimsonTransactions.filter(t => !t.isReconciled);
        const unreconciledBank = bankTransactions.filter(t => !t.isReconciled);
        const nritTransactions = bankTransactions.filter(t => t.isNrit);

        const totalCrimsonAmount = crimsonTransactions.reduce((sum, t) => sum + t.amount, 0);
        const totalBankAmount = bankTransactions.reduce((sum, t) => sum + t.amount, 0);
        const unreconciledCrimsonAmount = unreconciledCrimson.reduce((sum, t) => sum + t.amount, 0);
        const unreconciledBankAmount = unreconciledBank.reduce((sum, t) => sum + t.amount, 0);

        const reconciliationProgress = crimsonTransactions.length > 0 
            ? (reconciledCrimson.length / crimsonTransactions.length) * 100 
            : 0;

        return {
            totalCrimsonTransactions: crimsonTransactions.length,
            totalBankTransactions: bankTransactions.length,
            reconciledCrimsonTransactions: reconciledCrimson.length,
            reconciledBankTransactions: reconciledBank.length,
            unreconciledCrimsonAmount,
            unreconciledBankAmount,
            totalCrimsonAmount,
            totalBankAmount,
            reconciliationProgress,
            aiSuggestionsCount: aiSuggestions.length,
            nritTransactionsCount: nritTransactions.length,
        };
    }, [crimsonTransactions, bankTransactions, aiSuggestions]);

    const discrepancy = stats.unreconciledCrimsonAmount - stats.unreconciledBankAmount;
    const hasDiscrepancy = Math.abs(discrepancy) > 0.01;

    return (
        <div className="bg-white rounded-xl shadow-xl p-6 mb-6 border border-slate-200/50">
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-slate-800 flex items-center gap-3">
                    <ChartBarIcon className="w-8 h-8 text-sky-600" />
                    Reconciliation Dashboard
                </h2>
                <div className="text-sm text-slate-500">
                    Last updated: {new Date().toLocaleString()}
                </div>
            </div>

            {/* Key Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <StatCard
                    title="Reconciliation Progress"
                    value={formatPercentage(stats.reconciliationProgress)}
                    subtitle={`${stats.reconciledCrimsonTransactions} of ${stats.totalCrimsonTransactions} transactions`}
                    icon={<CheckCircleIcon className="w-6 h-6" />}
                    color="green"
                />
                
                <StatCard
                    title="Unreconciled Amount"
                    value={formatCurrency(Math.abs(discrepancy))}
                    subtitle={hasDiscrepancy ? "Discrepancy detected" : "Amounts match"}
                    icon={<ExclamationTriangleIcon className="w-6 h-6" />}
                    color={hasDiscrepancy ? "red" : "green"}
                />
                
                <StatCard
                    title="AI Suggestions"
                    value={stats.aiSuggestionsCount}
                    subtitle="Potential matches found"
                    icon={<SparklesIcon className="w-6 h-6" />}
                    color="purple"
                />
                
                <StatCard
                    title="NRIT Transactions"
                    value={stats.nritTransactionsCount}
                    subtitle="Non-reportable items"
                    icon={<ArchiveBoxIcon className="w-6 h-6" />}
                    color="indigo"
                />
            </div>

            {/* Progress Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-700 flex items-center gap-2">
                        <ClockIcon className="w-5 h-5" />
                        Reconciliation Progress
                    </h3>
                    <div className="space-y-3">
                        <div>
                            <div className="flex justify-between text-sm font-medium text-slate-600 mb-1">
                                <span>Overall Progress</span>
                                <span>{formatPercentage(stats.reconciliationProgress)}</span>
                            </div>
                            <ProgressBar progress={stats.reconciliationProgress} />
                        </div>
                        
                        <div>
                            <div className="flex justify-between text-sm font-medium text-slate-600 mb-1">
                                <span>Crimson Transactions</span>
                                <span>{stats.reconciledCrimsonTransactions}/{stats.totalCrimsonTransactions}</span>
                            </div>
                            <ProgressBar 
                                progress={(stats.reconciledCrimsonTransactions / stats.totalCrimsonTransactions) * 100} 
                                color="bg-sky-500"
                            />
                        </div>
                        
                        <div>
                            <div className="flex justify-between text-sm font-medium text-slate-600 mb-1">
                                <span>Bank Transactions</span>
                                <span>{stats.reconciledBankTransactions}/{stats.totalBankTransactions}</span>
                            </div>
                            <ProgressBar 
                                progress={(stats.reconciledBankTransactions / stats.totalBankTransactions) * 100} 
                                color="bg-indigo-500"
                            />
                        </div>
                    </div>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-700 flex items-center gap-2">
                        <CurrencyDollarIcon className="w-5 h-5" />
                        Amount Summary
                    </h3>
                    <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-sky-50 rounded-lg">
                            <span className="text-sm font-medium text-slate-600">Total Crimson Amount</span>
                            <span className="font-bold text-sky-700">{formatCurrency(stats.totalCrimsonAmount)}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-indigo-50 rounded-lg">
                            <span className="text-sm font-medium text-slate-600">Total Bank Amount</span>
                            <span className="font-bold text-indigo-700">{formatCurrency(stats.totalBankAmount)}</span>
                        </div>
                        <div className={`flex justify-between items-center p-3 rounded-lg ${hasDiscrepancy ? 'bg-rose-50' : 'bg-emerald-50'}`}>
                            <span className="text-sm font-medium text-slate-600">Unreconciled Difference</span>
                            <span className={`font-bold ${hasDiscrepancy ? 'text-rose-700' : 'text-emerald-700'}`}>
                                {formatCurrency(discrepancy)}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Quick Actions */}
            {(stats.aiSuggestionsCount > 0 || hasDiscrepancy) && (
                <div className="border-t border-slate-200 pt-6">
                    <h3 className="text-lg font-semibold text-slate-700 mb-4">Recommended Actions</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {stats.aiSuggestionsCount > 0 && (
                            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                                <div className="flex items-center gap-2 mb-2">
                                    <SparklesIcon className="w-5 h-5 text-purple-600" />
                                    <span className="font-semibold text-purple-800">Review AI Suggestions</span>
                                </div>
                                <p className="text-sm text-purple-700">
                                    {stats.aiSuggestionsCount} potential matches are waiting for your review.
                                </p>
                            </div>
                        )}
                        
                        {hasDiscrepancy && (
                            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div className="flex items-center gap-2 mb-2">
                                    <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
                                    <span className="font-semibold text-yellow-800">Investigate Discrepancy</span>
                                </div>
                                <p className="text-sm text-yellow-700">
                                    There's a {formatCurrency(Math.abs(discrepancy))} difference that needs attention.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};
