import React from 'react';

interface IconProps {
    className?: string;
    title?: string;
}

export const PlusIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
    </svg>
);

export const UploadIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
    </svg>
);

export const DocumentDuplicateIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
     <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
    </svg>
);

export const FilterIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
    </svg>
);

export const CheckCircleIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

export const SparklesIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m11-13l-1-1-1 1m4 4l-1-1-1 1M18 6l1-1-1-1M12 2v4m0 12v4m-4-7l-1 1 1 1m8-2l-1 1 1 1m-4-4l-1 1 1 1" />
    </svg>
);

export const LoadingIcon: React.FC<IconProps> = ({ className = 'w-5 h-5 animate-spin', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24">
      {title && <title>{title}</title>}
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

export const CogIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
      {title && <title>{title}</title>}
      <path strokeLinecap="round" strokeLinejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.096 2.572-1.065z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
);

export const ChevronDownIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
);

export const ChevronRightIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
    </svg>
);

export const LightBulbIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>
);

export const XCircleIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
     <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

export const FileTextIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor">
        {title && <title>{title}</title>}
        <path fillRule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a.375.375 0 01-.375-.375V6.75A3.75 3.75 0 0010.5 3h-4.875c0-1.036.84-1.875 1.875-1.875zM12.75 6a.75.75 0 00-1.5 0v6a.75.75 0 001.5 0v-6z" clipRule="evenodd" />
        <path d="M14.25 5.25a5.23 5.23 0 00-1.279-3.434 9.768 9.768 0 016.963 6.963A5.23 5.23 0 0014.25 5.25z" />
        <path d="M12 10.5a.75.75 0 01.75-.75h4.5a.75.75 0 010 1.5h-4.5a.75.75 0 01-.75-.75zM12 13.5a.75.75 0 01.75-.75h4.5a.75.75 0 010 1.5h-4.5a.75.75 0 01-.75-.75zM12 16.5a.75.75 0 01.75-.75h4.5a.75.75 0 010 1.5h-4.5a.75.75 0 01-.75-.75z" />
    </svg>
);

export const LinkIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="currentColor">
        {title && <title>{title}</title>}
        <path fillRule="evenodd" d="M12.97 3.97a.75.75 0 011.06 0l7.5 7.5a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 11-1.06-1.06l6.22-6.22H3a.75.75 0 010-1.5h16.19l-6.22-6.22a.75.75 0 010-1.06z" clipRule="evenodd" />
    </svg>
);

export const ArrowRightIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 20 20" fill="currentColor">
      {title && <title>{title}</title>}
      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-11.25a.75.75 0 00-1.5 0v4.59L7.3 9.24a.75.75 0 00-1.1 1.02l3.25 3.5a.75.75 0 001.1 0l3.25-3.5a.75.75 0 10-1.1-1.02l-1.95 2.1V6.75z" clipRule="evenodd" />
      <path fillRule="evenodd" d="M3 10a.75.75 0 01.75-.75h10.5a.75.75 0 010 1.5H3.75A.75.75 0 013 10z" clipRule="evenodd" />
    </svg>
);

export const QuickbooksIcon: React.FC<IconProps> = ({ className = 'w-8 h-8', title }) => (
    <svg className={className} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        {title && <title>{title}</title>}
        <circle cx="20" cy="20" r="20" fill="#2CA01C"></circle>
        <path d="M28.038 20.003c0 4.44-3.595 8.04-8.038 8.04-4.44 0-8.038-3.6-8.038-8.04 0-4.44 3.598-8.04 8.038-8.04 4.443 0 8.038 3.6 8.038 8.04" fill="#fff"></path>
        <path d="M25.044 20.003c0 2.78-2.26 5.03-5.044 5.03-2.78 0-5.04-2.25-5.04-5.04 0-2.78 2.26-5.03 5.04-5.03s5.044 2.25 5.044 5.03" fill="#2CA01C"></path>
        <path d="M21.23 22.064c0 .6-.2 1.08-.6 1.44a2.06 2.06 0 01-1.42.55c-.6 0-1.08-.18-1.44-.55-.36-.36-.54-.83-.54-1.44v-4.12h-1.6v4.12c0 .92.3 1.67.9 ********* 1.4.87 ********* 0 1.77-.3 2.37-.88.6-.6.9-1.34.9-2.24v-4.12h-1.6v4.12z" fill="#fff"></path>
    </svg>
);

export const PlaidIcon: React.FC<IconProps> = ({ className = 'w-8 h-8', title }) => (
    <svg className={className} viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
        {title && <title>{title}</title>}
        <circle cx="50" cy="50" r="50" fill="#000"></circle>
        <path d="M50.081 35.833c10.435 0 18.892 8.448 18.892 18.875 0 10.426-8.457 18.875-18.892 18.875-10.434 0-18.891-8.449-18.891-18.875C31.19 44.281 39.647 35.833 50.081 35.833zm-.045 4.708c-7.848 0-14.197 6.34-14.197 14.167 0 7.826 6.35 14.166 14.197 14.166 7.847 0 14.197-6.34 14.197-14.167s-6.35-14.167-14.197-14.167z" fill="#fff"></path>
        <path d="M66.45 54.708c-1.334 0-2.58.52-3.504 1.443a4.93 4.93 0 00-1.445 3.5c0 1.33.522 2.578 1.445 3.501a4.93 4.93 0 003.504 1.443c1.333 0 2.58-.52 3.503-1.443a4.93 4.93 0 001.445-3.5c0-1.332-.522-2.58-1.445-3.501a4.93 4.93 0 00-3.503-1.443zm-32.748 0c-1.333 0-2.58.52-3.503 1.443a4.93 4.93 0 00-1.445 3.5c0 1.33.522 2.578 1.445 3.501a4.93 4.93 0 003.503 1.443c1.334 0 2.58-.52 3.504-1.443a4.93 4.93 0 001.445-3.5c0-1.332-.52-2.58-1.445-3.501a4.93 4.93 0 00-3.504-1.443z" fill="#fff"></path>
    </svg>
);

export const ScissorsIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M14.412 7.588a4.5 4.5 0 015.656 0 .5.5 0 00.708-.708 5.5 5.5 0 00-7.778 0 .5.5 0 00.707.707zm-2.828 0a.5.5 0 01.707 0 4.5 4.5 0 015.657 0 .5.5 0 00.707-.707 5.5 5.5 0 00-7.778 0 .5.5 0 00.707.707zM14.412 10.412a4.5 4.5 0 010 5.657.5.5 0 00-.707.707 5.5 5.5 0 000-7.778.5.5 0 00.707.707zm-2.828 0a.5.5 0 010 .707 4.5 4.5 0 010 5.657.5.5 0 00-.707.707 5.5 5.5 0 000-7.778.5.5 0 00.707 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.465 16.535a4.5 4.5 0 01-5.657 0 .5.5 0 00-.707.707 5.5 5.5 0 007.778 0 .5.5 0 00-.707-.707zm2.828 0a.5.5 0 01-.707 0 4.5 4.5 0 01-5.657 0 .5.5 0 00-.707.707 5.5 5.5 0 007.778 0 .5.5 0 000-.707zM8.465 13.588a4.5 4.5 0 010-5.657.5.5 0 00.707-.707 5.5 5.5 0 000 7.778.5.5 0 00-.707-.707zm2.828 0a.5.5 0 01.707 0 4.5 4.5 0 010-5.657.5.5 0 00.707-.707 5.5 5.5 0 000 7.778.5.5 0 000-.707z" />
    </svg>
);

export const EyeSlashIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243L6.228 6.228" />
    </svg>
);

export const ArchiveBoxIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="m20.25 7.5-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
    </svg>
);

export const ArrowUturnLeftIcon: React.FC<IconProps> = ({ className = 'w-5 h-5', title }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
        {title && <title>{title}</title>}
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
    </svg>
);