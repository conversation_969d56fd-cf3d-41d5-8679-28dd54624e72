import React from 'react';

interface ReconciliationActionsProps {
    crimsonSelectedTotal: number;
    bankSelectedTotal: number;
    crimsonUnreconciledTotal: number;
    bankUnreconciledTotal: number;
    onReconcile: () => void;
}

const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

export const ReconciliationActions: React.FC<ReconciliationActionsProps> = ({
    crimsonSelectedTotal,
    bankSelectedTotal,
    crimsonUnreconciledTotal,
    bankUnreconciledTotal,
    onReconcile,
}) => {
    const difference = crimsonSelectedTotal - bankSelectedTotal;
    const canReconcile = difference === 0 && crimsonSelectedTotal !== 0;

    const getDifferenceColor = () => {
        if (canReconcile) return 'text-emerald-500';
        if (crimsonSelectedTotal !== 0 || bankSelectedTotal !== 0) return 'text-rose-500';
        return 'text-slate-500';
    }

    const Stat: React.FC<{label: string; value: string; unreconciled?: string; className?: string}> = ({label, value, unreconciled, className}) => (
        <div className={`flex flex-col text-center p-4 rounded-lg ${className}`}>
             <span className="text-sm font-semibold text-slate-500 uppercase tracking-wider">{label}</span>
             <span className="text-2xl font-bold mt-1">{value}</span>
             {unreconciled && <span className="text-xs text-rose-600 font-medium mt-1">({unreconciled} Unreconciled)</span>}
        </div>
    );


    return (
        <div className="bg-white rounded-xl shadow-lg p-4 border border-slate-200/80">
            <div className="grid grid-cols-1 md:grid-cols-5 items-center gap-4">
                
                <Stat label="Crimson" value={formatCurrency(crimsonSelectedTotal)} unreconciled={formatCurrency(crimsonUnreconciledTotal)} className="text-sky-700"/>
                
                <div className="flex justify-center items-center text-slate-300 text-2xl font-light">-</div>
                
                <Stat label="Bank" value={formatCurrency(bankSelectedTotal)} unreconciled={formatCurrency(bankUnreconciledTotal)} className="text-indigo-700"/>

                <div className="flex flex-col text-center p-4">
                     <span className="text-sm font-semibold text-slate-500 uppercase tracking-wider">DIFFERENCE</span>
                    <span className={`text-2xl font-bold mt-1 ${getDifferenceColor()}`}>{formatCurrency(difference)}</span>
                </div>

                <button
                    onClick={onReconcile}
                    disabled={!canReconcile}
                    className="w-full px-4 py-4 bg-emerald-600 text-white font-bold text-lg rounded-lg shadow-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:bg-slate-300 disabled:cursor-not-allowed disabled:shadow-none transition-all duration-200"
                >
                    Reconcile
                </button>
            </div>
        </div>
    );
};