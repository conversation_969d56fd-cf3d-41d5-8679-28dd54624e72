import React, { use<PERSON>emo } from 'react';
import { AnyTransaction, CrimsonTransaction, BankTransaction, TransactionType, MatchedPair } from '../types';
import { CheckCircleIcon, ChevronDownIcon, ChevronRightIcon, LightBulbIcon, ScissorsIcon, EyeSlashIcon, ArchiveBoxIcon, ArrowUturnLeftIcon } from './Icons';

interface TransactionTableProps {
    transactions: AnyTransaction[];
    transactionType: TransactionType;
    selectedIds: Set<string>;
    onSelectionChange: React.Dispatch<React.SetStateAction<Set<string>>>;
    aiSuggestions: MatchedPair[];
    expandedRows: Set<string>;
    onToggleExpand: React.Dispatch<React.SetStateAction<Set<string>>>;
    onOpenSplitModal?: (transaction: BankTransaction) => void;
    onMarkAsNrit?: (transactionId: string) => void;
    onUnmarkAsNrit?: (transactionId: string) => void;
}

const isCrimsonTransaction = (t: AnyTransaction): t is CrimsonTransaction => (t as CrimsonTransaction).moneyType !== undefined;
const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

const TransactionRow: React.FC<{
    transaction: AnyTransaction;
    isSelected: boolean;
    onToggleSelection: (id: string) => void;
    isCrimson: boolean;
    isExpanded: boolean;
    onToggleExpand: (id: string) => void;
    suggestionReason: string | null;
    onOpenSplitModal?: (transaction: BankTransaction) => void;
    onMarkAsNrit?: (transactionId: string) => void;
    onUnmarkAsNrit?: (transactionId: string) => void;
}> = ({ transaction, isSelected, onToggleSelection, isCrimson, isExpanded, onToggleExpand, suggestionReason, onOpenSplitModal, onMarkAsNrit, onUnmarkAsNrit }) => {
    
    const amountColor = transaction.amount >= 0 ? 'text-emerald-700' : 'text-rose-700';
    
    let rowStyle = 'bg-white';
    if (transaction.isReconciled) {
        rowStyle = 'bg-white text-slate-400 italic';
    } else if (isSelected) {
        rowStyle = 'bg-sky-100/50';
    } else if (suggestionReason) {
        rowStyle = 'bg-sky-50';
    }

    let borderStyle = suggestionReason && !transaction.isReconciled ? 'border-l-4 border-sky-500' : 'border-l-4 border-transparent';
    if(isSelected) {
        borderStyle = 'border-l-4 border-sky-600'
    }

    const hasDetails = (isCrimson && (transaction as CrimsonTransaction).batchDetails) || (!isCrimson && (transaction as BankTransaction).splitDetails);

    return (
        <>
            <tr className={`border-b border-slate-200 transition-colors duration-150 even:bg-slate-50/50 ${rowStyle}`}>
                <td className={`pl-2 py-2.5 text-center ${borderStyle}`}>
                    <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => onToggleSelection(transaction.id)}
                        className="h-4 w-4 rounded border-slate-300 text-sky-600 focus:ring-sky-500"
                        disabled={transaction.isReconciled}
                    />
                </td>
                <td className="px-2 py-2.5 text-sm w-12 text-center">
                    {hasDetails ? (
                        <button onClick={() => onToggleExpand(transaction.id)} className="p-1 rounded-full hover:bg-slate-200 text-slate-500">
                            {isExpanded ? <ChevronDownIcon /> : <ChevronRightIcon />}
                        </button>
                    ) : <div className="w-5 h-5"></div>}
                </td>
                <td className="px-2 py-2.5 text-sm whitespace-nowrap">{transaction.date}</td>
                {isCrimson && isCrimsonTransaction(transaction) && <td className="px-2 py-2.5 text-sm">{transaction.moneyType}</td>}
                <td className={`px-2 py-2.5 text-sm ${!isCrimson ? 'w-full' : ''} ${transaction.isReconciled ? '' : 'text-slate-600'}`}>
                    {!isCrimson ? (transaction as BankTransaction).description : (transaction as CrimsonTransaction).paymentType}
                </td>
                <td className={`px-2 py-2.5 text-sm text-right font-mono ${amountColor}`}>{formatCurrency(transaction.amount)}</td>
                <td className="px-2 py-2.5 text-sm text-center w-16">
                    <div className="flex justify-center items-center gap-2">
                         {transaction.isReconciled && (
                            (transaction as BankTransaction).isNrit ? (
                                <div className="flex items-center gap-2">
                                    <ArchiveBoxIcon className="h-5 w-5 text-indigo-500" title="Non-Reportable (NRIT)" />
                                    {onUnmarkAsNrit && (
                                        <button 
                                            onClick={() => onUnmarkAsNrit(transaction.id)} 
                                            className="text-slate-400 hover:text-sky-600"
                                            title="Restore Transaction"
                                        >
                                            <ArrowUturnLeftIcon className="h-4 w-4" />
                                        </button>
                                    )}
                                </div>
                            ) : (
                                <CheckCircleIcon className="h-5 w-5 text-emerald-500" title="Reconciled" />
                            )
                        )}
                        {suggestionReason && !transaction.isReconciled && (
                             <div className="relative group">
                                <LightBulbIcon className="h-5 w-5 text-sky-500" />
                                <div className="absolute top-full mt-2 w-64 right-0 p-2 bg-slate-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                                    <strong>AI Suggestion:</strong> {suggestionReason}
                                </div>
                            </div>
                        )}
                         {!isCrimson && !transaction.isReconciled && (
                            <>
                                {onOpenSplitModal && (
                                    <button 
                                        onClick={() => onOpenSplitModal(transaction as BankTransaction)} 
                                        className="text-slate-400 hover:text-sky-600"
                                        title="Split Transaction"
                                    >
                                        <ScissorsIcon className="h-5 w-5" />
                                    </button>
                                )}
                                {onMarkAsNrit && (
                                     <button 
                                        onClick={() => onMarkAsNrit(transaction.id)} 
                                        className="text-slate-400 hover:text-orange-600"
                                        title="Mark as Non-Reportable (NRIT)"
                                    >
                                        <EyeSlashIcon className="h-5 w-5" />
                                    </button>
                                )}
                            </>
                         )}
                    </div>
                </td>
            </tr>
            {isExpanded && hasDetails && (
                 <tr className={`${rowStyle} border-b border-slate-200`}>
                    <td colSpan={isCrimson ? 7 : 6} className="p-0">
                        <div className="bg-slate-100 p-3 pl-16">
                            {isCrimsonTransaction(transaction) && transaction.batchDetails && (
                                <div className="text-xs text-slate-600">
                                    <h4 className="font-bold text-slate-700 mb-1">Batch Details</h4>
                                    <ul>{transaction.batchDetails.map(d => <li key={d.id} className="flex justify-between py-0.5"><span>{d.donor}</span> <span className="font-mono text-slate-800">{formatCurrency(d.amount)}</span></li>)}</ul>
                                </div>
                            )}
                            {!isCrimsonTransaction(transaction) && transaction.splitDetails && (
                                <div className="text-xs text-slate-600">
                                     <h4 className="font-bold text-slate-700 mb-1">Split Details</h4>
                                     <ul>
                                        <li className="flex justify-between py-0.5"><span>Gross Amount</span> <span className="font-mono text-slate-800">{formatCurrency(transaction.splitDetails.gross)}</span></li>
                                        <li className="flex justify-between py-0.5"><span>Chargebacks/Refunds</span> <span className="font-mono text-slate-800">{formatCurrency(transaction.splitDetails.chargebacks)}</span></li>
                                        <li className="flex justify-between py-0.5"><span>Fees</span> <span className="font-mono text-slate-800">{formatCurrency(transaction.splitDetails.fees)}</span></li>
                                     </ul>
                                </div>
                            )}
                        </div>
                    </td>
                 </tr>
            )}
        </>
    );
}

export const TransactionTable: React.FC<TransactionTableProps> = ({ transactions, transactionType, selectedIds, onSelectionChange, aiSuggestions, expandedRows, onToggleExpand, onOpenSplitModal, onMarkAsNrit, onUnmarkAsNrit }) => {
    const isCrimson = transactionType === TransactionType.CRIMSON;

    const suggestionMap = useMemo(() => {
        const map = new Map<string, string>();
        aiSuggestions.forEach(s => {
            if (isCrimson) {
                map.set(s.crimsonTransactionId, s.reasoning);
            } else {
                s.bankTransactionId.forEach(bId => map.set(bId, s.reasoning));
            }
        });
        return map;
    }, [aiSuggestions, isCrimson]);

    const handleToggleSelection = (id: string) => {
        onSelectionChange(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) newSet.delete(id);
            else newSet.add(id);
            return newSet;
        });
    };
    
    const handleToggleExpand = (id: string) => {
         onToggleExpand(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) newSet.delete(id);
            else newSet.add(id);
            return newSet;
        });
    }

    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            const allUnreconciledIds = transactions.filter(t => !t.isReconciled).map(t => t.id);
            onSelectionChange(new Set(allUnreconciledIds));
        } else {
            onSelectionChange(new Set());
        }
    };
    
    const unreconciledCount = transactions.filter(t => !t.isReconciled).length;
    const allSelected = unreconciledCount > 0 && selectedIds.size === unreconciledCount;

    return (
        <div className="overflow-x-auto h-full">
            <table className="w-full text-left text-sm table-fixed">
                <thead className="bg-slate-100 text-slate-600 sticky top-0 z-10">
                    <tr className="border-b-2 border-slate-300">
                        <th className="px-3 py-2 text-center" style={{width: '5%'}}>
                            <input
                                type="checkbox"
                                onChange={handleSelectAll}
                                checked={allSelected}
                                disabled={unreconciledCount === 0}
                                className="h-4 w-4 rounded border-slate-400 bg-slate-100 text-sky-600 focus:ring-sky-500 focus:ring-offset-slate-100"
                            />
                        </th>
                        <th className="px-2 py-2" style={{width: '8%'}}></th>
                        <th className="px-2 py-2 font-semibold" style={{width: isCrimson ? '15%' : '17%'}}>Date</th>
                        {isCrimson && <th className="px-2 py-2 font-semibold" style={{width: '22%'}}>Type</th>}
                        <th className="px-2 py-2 font-semibold">{isCrimson ? 'Payment Type' : 'Description'}</th>
                        <th className="px-2 py-2 text-right font-semibold" style={{width: '20%'}}>Amount</th>
                        <th className="px-2 py-2 text-center font-semibold" style={{width: '15%'}}>Actions</th>
                    </tr>
                </thead>
                <tbody className="bg-white">
                    {transactions.length === 0 ? (
                         <tr>
                            <td colSpan={isCrimson ? 7 : 6} className="text-center py-10 text-slate-500">No transactions to display.</td>
                        </tr>
                    ) : (
                         transactions.map(t => (
                            <TransactionRow 
                                key={t.id}
                                transaction={t}
                                isSelected={selectedIds.has(t.id)}
                                onToggleSelection={handleToggleSelection}
                                isCrimson={isCrimson}
                                isExpanded={expandedRows.has(t.id)}
                                onToggleExpand={handleToggleExpand}
                                suggestionReason={suggestionMap.get(t.id) || null}
                                onOpenSplitModal={onOpenSplitModal}
                                onMarkAsNrit={onMarkAsNrit}
                                onUnmarkAsNrit={onUnmarkAsNrit}
                            />
                        ))
                    )}
                </tbody>
            </table>
        </div>
    );
};